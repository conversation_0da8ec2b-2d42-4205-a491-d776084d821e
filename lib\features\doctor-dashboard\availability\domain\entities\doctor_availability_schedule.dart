import 'package:equatable/equatable.dart';

/// Entity representing a doctor's complete availability schedule
class DoctorAvailabilitySchedule extends Equatable {
  /// Start date of the availability period
  final DateTime startDate;
  
  /// End date of the availability period
  final DateTime endDate;
  
  /// Whether the availability pattern should repeat
  final bool isRepeat;
  
  /// List of dates when the doctor is unavailable
  final List<UnavailableDate> unavailableDates;
  
  /// Weekly schedule for each day of the week
  final List<DaySchedule> days;

  const DoctorAvailabilitySchedule({
    required this.startDate,
    required this.endDate,
    required this.isRepeat,
    required this.unavailableDates,
    required this.days,
  });

  @override
  List<Object?> get props => [
        startDate,
        endDate,
        isRepeat,
        unavailableDates,
        days,
      ];

  /// Creates a copy of this schedule with the given fields replaced with new values
  DoctorAvailabilitySchedule copyWith({
    DateTime? startDate,
    DateTime? endDate,
    bool? isRepeat,
    List<UnavailableDate>? unavailableDates,
    List<DaySchedule>? days,
  }) {
    return DoctorAvailabilitySchedule(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isRepeat: isRepeat ?? this.isRepeat,
      unavailableDates: unavailableDates ?? this.unavailableDates,
      days: days ?? this.days,
    );
  }
}

/// Entity representing an unavailable date for the doctor
class UnavailableDate extends Equatable {
  /// The date when the doctor is unavailable
  final DateTime date;
  
  /// Whether the entire day is unavailable
  final bool isFullDay;
  
  /// Specific time slot when unavailable (only if not full day)
  final AvailabilityTimeSlot? timeSlot;

  const UnavailableDate({
    required this.date,
    required this.isFullDay,
    this.timeSlot,
  });

  @override
  List<Object?> get props => [date, isFullDay, timeSlot];

  /// Creates a copy of this unavailable date with the given fields replaced
  UnavailableDate copyWith({
    DateTime? date,
    bool? isFullDay,
    AvailabilityTimeSlot? timeSlot,
  }) {
    return UnavailableDate(
      date: date ?? this.date,
      isFullDay: isFullDay ?? this.isFullDay,
      timeSlot: timeSlot ?? this.timeSlot,
    );
  }
}

/// Entity representing a day's schedule in the weekly availability
class DaySchedule extends Equatable {
  /// Day of the week (e.g., "monday", "tuesday", etc.)
  final String day;
  
  /// Whether this is an off day (no availability)
  final bool isOffDay;
  
  /// List of available time slots for this day
  final List<AvailabilityTimeSlot> slots;

  const DaySchedule({
    required this.day,
    required this.isOffDay,
    required this.slots,
  });

  @override
  List<Object?> get props => [day, isOffDay, slots];

  /// Creates a copy of this day schedule with the given fields replaced
  DaySchedule copyWith({
    String? day,
    bool? isOffDay,
    List<AvailabilityTimeSlot>? slots,
  }) {
    return DaySchedule(
      day: day ?? this.day,
      isOffDay: isOffDay ?? this.isOffDay,
      slots: slots ?? this.slots,
    );
  }
}

/// Entity representing a time slot for availability
class AvailabilityTimeSlot extends Equatable {
  /// Start time in HH:MM:SS format (24-hour)
  final String startTime;
  
  /// End time in HH:MM:SS format (24-hour)
  final String endTime;

  const AvailabilityTimeSlot({
    required this.startTime,
    required this.endTime,
  });

  @override
  List<Object?> get props => [startTime, endTime];

  /// Creates a copy of this time slot with the given fields replaced
  AvailabilityTimeSlot copyWith({
    String? startTime,
    String? endTime,
  }) {
    return AvailabilityTimeSlot(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }

  /// Converts 12-hour format time (e.g., "7:30 AM") to 24-hour format (e.g., "07:30:00")
  static String convertTo24HourFormat(String time12Hour) {
    final parts = time12Hour.split(' ');
    if (parts.length != 2) return time12Hour;
    
    final timePart = parts[0];
    final period = parts[1].toUpperCase();
    
    final timeParts = timePart.split(':');
    if (timeParts.length != 2) return time12Hour;
    
    int hour = int.tryParse(timeParts[0]) ?? 0;
    final minute = timeParts[1];
    
    if (period == 'PM' && hour != 12) {
      hour += 12;
    } else if (period == 'AM' && hour == 12) {
      hour = 0;
    }
    
    return '${hour.toString().padLeft(2, '0')}:$minute:00';
  }

  /// Converts 24-hour format time (e.g., "07:30:00") to 12-hour format (e.g., "7:30 AM")
  static String convertTo12HourFormat(String time24Hour) {
    final parts = time24Hour.split(':');
    if (parts.length < 2) return time24Hour;
    
    int hour = int.tryParse(parts[0]) ?? 0;
    final minute = parts[1];
    
    String period = 'AM';
    if (hour >= 12) {
      period = 'PM';
      if (hour > 12) hour -= 12;
    } else if (hour == 0) {
      hour = 12;
    }
    
    return '$hour:$minute $period';
  }
}
