import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_departments/get_departments_data.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_departments/get_departments_response.dart';

class GetDepartmentsResponseModel extends GetDepartmentsResponse {
  const GetDepartmentsResponseModel({
    required super.data,
    required super.total,
    required super.skip,
    required super.limit,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory GetDepartmentsResponseModel.fromJson(Map<String, dynamic> json) {
    return GetDepartmentsResponseModel(
      data: GetDepartmentsData(
        departments:
            (json['data'] as List)
                .map(
                  (item) => GetDepartmentsDataItems(
                    value: item['value'] as String,
                    label: item['label'] as String,
                  ),
                )
                .toList(),
      ),
      total: json['total'],
      skip: json['skip'],
      limit: json['limit'],
      message: json['message'],
      status: json['status'],
      statusCode: json['statusCode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data':
          data.departments
              .map((item) => {'value': item.value, 'label': item.label})
              .toList(),
      'total': total,
      'skip': skip,
      'limit': limit,
      'message': message,
      'status': status,
      'statusCode': statusCode,
    };
  }
}
