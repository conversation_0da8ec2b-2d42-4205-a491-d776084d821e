import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import '../entities/doctor_availability.dart';
import '../repositories/availability_repository.dart';

class GetDoctorAvailability implements UseCase<DoctorAvailability, GetDoctorAvailabilityParams> {
  final AvailabilityRepository repository;

  GetDoctorAvailability(this.repository);

  @override
  Future<Either<Failure, DoctorAvailability>> call(GetDoctorAvailabilityParams params) async {
    return await repository.getDoctorAvailability(
      doctorId: params.doctorId,
      date: params.date,
    );
  }
}

class GetDoctorAvailabilityParams extends Equatable {
  final String doctorId;
  final DateTime date;

  const GetDoctorAvailabilityParams({
    required this.doctorId,
    required this.date,
  });

  @override
  List<Object?> get props => [doctorId, date];
}
