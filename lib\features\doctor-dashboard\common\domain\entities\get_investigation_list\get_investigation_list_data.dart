import 'package:equatable/equatable.dart';

class GetInvestigationListData extends Equatable {
  final List<Investigation> data;

  const GetInvestigationListData({required this.data});

  @override
  List<Object?> get props => [data];
}

class Investigation extends Equatable {
  final String value;
  final String name;
  final String description;

  const Investigation({
    required this.value,
    required this.name,
    required this.description,
  });

  @override
  List<Object?> get props => [value, name, description];
}
