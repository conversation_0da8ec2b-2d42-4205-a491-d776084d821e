import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_hospitals/get_hospitals_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_hospitals/get_hospitals_response_model.dart';

abstract class GetHospitalsRepositories {
  Future<Either<Failure, GetHospitalsResponseModel>> getAllHospitals(
    GetHospitalsRequestModel requestModel,
  );
}
