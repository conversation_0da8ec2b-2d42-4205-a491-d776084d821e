import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_request.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_response.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/update_appointment_status_by_appointmentid_repositories.dart';

class UpdateAppointmentStatusByAppointmentid
    implements
        UseCase<
          UpdateAppointmentStatusByAppointmentidResponse,
          UpdateAppointmentStatusByAppointmentidRequest
        > {
  final UpdateAppointmentStatusByAppointmentidRepositories repository;

  UpdateAppointmentStatusByAppointmentid(this.repository);

  @override
  Future<Either<Failure, UpdateAppointmentStatusByAppointmentidResponse>> call(
    UpdateAppointmentStatusByAppointmentidRequest params,
  ) async {
    final requestModel =
        UpdateAppointmentStatusByAppointmentidRequestModel.fromEntity(params);
    return await repository.updateAppointmentStatusByAppointmentid(
      requestModel,
    );
  }
}
