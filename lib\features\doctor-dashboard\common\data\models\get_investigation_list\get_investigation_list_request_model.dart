import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_investigation_list/get_investigation_list_request.dart';

class GetInvestigationListRequestModel extends GetInvestigationListRequest {
  const GetInvestigationListRequestModel({
    required super.skip,
    required super.limit,
  });

  factory GetInvestigationListRequestModel.fromEntity(
    GetInvestigationListRequest entity,
  ) {
    return GetInvestigationListRequestModel(
      skip: entity.skip,
      limit: entity.limit,
    );
  }

  Map<String, dynamic> toJson() {
    return {'skip': skip, 'limit': limit};
  }
}
