import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_details_by_id/get_appointments_details_by_id_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointment_details_by_id/get_appointment_details_by_id_response.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointment_details_by_id/get_appointment_details_by_id_request.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointment_details_by_id_repositories.dart';

class GetAppointmentDetailsById
    implements
        UseCase<
          GetAppointmentDetailsByIDResponse,
          GetAppointmentDetailsByIDRequest
        > {
  final GetAppointmentDetailsByIDRepositories repository;

  GetAppointmentDetailsById(this.repository);

  @override
  Future<Either<Failure, GetAppointmentDetailsByIDResponse>> call(
    GetAppointmentDetailsByIDRequest params,
  ) async {
    final requestModel = GetAppointmentsDetailsByIDRequestModel.fromEntity(
      params,
    );
    return await repository.getAppointmentDetailsById(requestModel);
  }
}
