import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/appointment/get_appointment_details_by_id_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_details_by_id/get_appointments_details_by_id_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_details_by_id/get_appointments_details_by_id_response_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointment_details_by_id_repositories.dart';

class GetAppointmentDetailsByIDRepositoriesImpl
    implements GetAppointmentDetailsByIDRepositories {
  final GetAppointmentDetailsByIDRemoteDatasource remoteDatasource;

  GetAppointmentDetailsByIDRepositoriesImpl({required this.remoteDatasource});

  @override
  Future<Either<Failure, GetAppointmentDetailsByIDResponseModel>>
  getAppointmentDetailsById(
    GetAppointmentsDetailsByIDRequestModel request,
  ) async {
    try {
      final response = await remoteDatasource.getAppointmentDetailsById(
        request,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
