import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_designation_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_designation/get_designation_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_designation/get_designation_response_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_designations_repositories.dart';

class GetDesignationsRepositoriesImpl implements GetDesignationsRepositories {
  final GetDesignationsRemoteDatasource remoteDatasource;

  GetDesignationsRepositoriesImpl({required this.remoteDatasource});

  @override
  Future<Either<Failure, GetDesignationsResponseModel>> getAllDesignations(
    GetDesignationsRequestModel requestModel,
  ) async {
    try {
      final response = await remoteDatasource.getAllDesignations(requestModel);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
