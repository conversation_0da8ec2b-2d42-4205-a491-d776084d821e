import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_request.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_response.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/analytics/get_doctor_appointment_statistics_repositories.dart';

class GetDoctorAppointmentStatistics
    implements
        UseCase<
          GetDoctorAppointmentStatisticsResponse,
          GetDoctorAppointmentStatisticsRequest
        > {
  final GetDoctorAppointmentStatisticsRepositories repository;

  GetDoctorAppointmentStatistics(this.repository);

  @override
  Future<Either<Failure, GetDoctorAppointmentStatisticsResponse>> call(
    GetDoctorAppointmentStatisticsRequest params,
  ) async {
    final requestModel = GetDoctorAppointmentStatisticsRequestModel.fromEntity(
      params,
    );
    return await repository.getDoctorAppointmentStatistics(requestModel);
  }
}
