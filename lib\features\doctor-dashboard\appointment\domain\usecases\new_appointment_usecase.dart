import '../../data/models/new_appointment_model.dart';
import '../../data/repositories/appointment_repository.dart';
import '../entities/new_appointment_request.dart';

class NewAppointmentUsecase {
  final AppointmentRepository repository;

  NewAppointmentUsecase(this.repository);

  Future<NewAppointmentModel> call(NewAppointmentRequest request) async {
    return repository.createAppointment(request);
  }
}