import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_by_doctor/get_appointments_by_doctor_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_by_doctor/get_appointments_by_doctor_response_model.dart';

abstract class GetAppointmentByDoctorRepositories {
  Future<Either<Failure, GetAppointmentsByDoctorResponseModel>>
  getAppointmentsByDoctor(GetAppointmentsByDoctorRequestModel request);
}
