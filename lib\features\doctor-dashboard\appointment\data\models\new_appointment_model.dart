
class NewAppointmentModel {
  final String bookingId;
  final String slotDate;
  final String startTime;
  final String endTime;

  NewAppointmentModel({
    required this.bookingId,
    required this.slotDate,
    required this.startTime,
    required this.endTime,
  });

  factory NewAppointmentModel.fromJson(Map<String, dynamic> json) {

    final data = json['data'] as Map<String, dynamic>;
    return NewAppointmentModel(
      bookingId: data['booking_id'] as String,
      slotDate: data['slot_date'] as String,
      startTime: data['start_time'] as String,
      endTime: data['end_time'] as String,
    );
  }
}