import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/widgets/company-icon.dart';

class SideBar extends StatelessWidget {
  final List<SideBarListItem>? menuItems;
  const SideBar({super.key, required this.menuItems});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 240,
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        border: Border(right: BorderSide(color: Color(0xFFE0E0E0), width: 1)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(height: AppConstants.sizedBoxHeightLarge),
          CompanyIcon(),
          SizedBox(height: AppConstants.sizedBoxHeightLarge),
          Expanded(
            child: SingleChildScrollView(
              child: SideBarMenu(menuItems: menuItems),
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightLarge),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingSmall,
            ),
            child: SideBarMenuItem(
              title: 'Log Out',
              icon: 'assets/icons/doctor/power.svg',
              nonSelectedIconColor: AppConstants.errorColor,
              nonSelectedTextColor: AppConstants.errorColor,
              onTap: () {},
            ),
          ),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          Divider(color: Color(0xFF97B4F9), height: 1),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
          Padding(padding: const EdgeInsets.all(12.0), child: CallUs()),
          SizedBox(height: AppConstants.sizedBoxHeightSmall),
        ],
      ),
    );
  }
}

class SideBarMenu extends StatefulWidget {
  final Function(int)? onMenuItemTap;
  final Function(int, int)? onSubMenuItemTap;
  final double padding;
  final List<SideBarListItem>? menuItems;
  const SideBarMenu({
    super.key,
    this.onMenuItemTap,
    this.onSubMenuItemTap,
    this.padding = AppConstants.paddingMedium,
    required this.menuItems,
  });

  @override
  State<SideBarMenu> createState() => _SideBarMenuState();
}

class _SideBarMenuState extends State<SideBarMenu> {
  int selectedMenuIndex = 0;
  int selectedSubMenuIndex = -1;

  void onItemTap(int index, {int subIndex = -1}) {
    setState(() {
      selectedMenuIndex = index;
      selectedSubMenuIndex = subIndex;
    });
  }

  void onSubMenuItemTap(int menuIndex, int subIndex) {
    onItemTap(menuIndex, subIndex: subIndex);
    if (widget.onSubMenuItemTap != null) {
      widget.onSubMenuItemTap!(menuIndex, subIndex);
    }
  }

  void onMenuItemTap(int menuIndex) {
    onItemTap(menuIndex);
    if (widget.onMenuItemTap != null) {
      widget.onMenuItemTap!(menuIndex);
    }
  }

  bool isCurrentRoute(String path) {
    final location = GoRouterState.of(context).matchedLocation;
    return location == path;
  }

  void updateSelectedIndex() {
    final location = GoRouterState.of(context).matchedLocation;
    for (int i = 0; i < (widget.menuItems?.length ?? 0); i++) {
      final item = widget.menuItems![i];
      if (location == item.route) {
        selectedMenuIndex = i;
        selectedSubMenuIndex = -1;
        return;
      }
      if (item.subItems != null) {
        for (int j = 0; j < item.subItems!.length; j++) {
          final subItem = item.subItems![j];
          if (location == subItem.route) {
            selectedMenuIndex = i;
            selectedSubMenuIndex = j;
            return;
          }
        }
      }
    }
    selectedMenuIndex = -1;
    selectedSubMenuIndex = -1;
  }

  @override
  Widget build(BuildContext context) {
    updateSelectedIndex();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: widget.padding),
      child: Column(
        spacing: 10,
        children: [
          ...(widget.menuItems ?? []).asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return SideBarMenuItem(
              title: item.title,
              icon: item.icon,
              isSelected: selectedMenuIndex == index,
              onTap: () {
                onMenuItemTap(index);
                if (item.route.isNotEmpty) {
                  context.go(item.route);
                }
              },
              subItems:
                  item.subItems?.asMap().entries.map((subEntry) {
                    final subIndex = subEntry.key;
                    final subItem = subEntry.value;
                    return SideBarMenuItem(
                      title: subItem.title,
                      icon: subItem.icon,
                      isSelected:
                          selectedMenuIndex == index &&
                          selectedSubMenuIndex == subIndex,
                      onTap: () {
                        onSubMenuItemTap(index, subIndex);
                        if (subItem.route.isNotEmpty) {
                          context.go(subItem.route);
                        }
                      },
                    );
                  }).toList(),
            );
          }),
        ],
      ),
    );
  }
}

class SideBarMenuItem extends StatefulWidget {
  final String title;
  final String icon;
  final bool isSelected;
  final List<SideBarMenuItem>? subItems;
  final VoidCallback onTap;
  final Color nonSelectedIconColor;
  final Color selectedIconColor;
  final Color selectedTextolor;
  final Color nonSelectedTextColor;

  const SideBarMenuItem({
    super.key,
    required this.title,
    required this.icon,
    required this.onTap,
    this.isSelected = false,
    this.nonSelectedIconColor = AppConstants.textMidColor,
    this.selectedIconColor = Colors.white,
    this.subItems,
    this.selectedTextolor = Colors.white,
    this.nonSelectedTextColor = AppConstants.textMidColor,
  });

  @override
  State<SideBarMenuItem> createState() => _SideBarMenuItemState();
}

class _SideBarMenuItemState extends State<SideBarMenuItem> {
  bool _isExpanded = false;

  void _toggleDropdown() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    final hasSubItems = widget.subItems != null && widget.subItems!.isNotEmpty;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: widget.onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12.0,
              vertical: AppConstants.paddingSmall,
            ),
            decoration: BoxDecoration(
              color:
                  widget.isSelected
                      ? AppConstants.primaryColor
                      : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                ReusableSvgImage(
                  assetPath: widget.icon,
                  width: 18,
                  height: 18,
                  color:
                      widget.isSelected
                          ? widget.selectedIconColor
                          : widget.nonSelectedIconColor,
                ),
                const SizedBox(width: AppConstants.sizedBoxHeightMedium),
                Text(
                  widget.title,
                  style: TextStyle(
                    color:
                        widget.isSelected
                            ? widget.selectedTextolor
                            : widget.nonSelectedTextColor,
                    fontWeight: FontWeight.w400,
                    fontSize: AppConstants.fontSizeLarge,
                  ),
                ),
                const Spacer(),
                if (hasSubItems)
                  GestureDetector(
                    onTap: _toggleDropdown,
                    child: ReusableSvgImage(
                      assetPath: 'assets/icons/doctor/dropdown.svg',
                      color:
                          widget.isSelected
                              ? Colors.white
                              : AppConstants.textMidColor,
                    ),
                  ),
              ],
            ),
          ),
        ),
        if (hasSubItems && _isExpanded)
          Padding(
            padding: const EdgeInsets.only(left: 32.0, top: 10.0, bottom: 10.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 10,
              children: widget.subItems!,
            ),
          ),
      ],
    );
  }
}

class CallUs extends StatelessWidget {
  const CallUs({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ReusableSvgImage(assetPath: 'assets/icons/doctor/call.svg'),
        const SizedBox(width: AppConstants.sizedBoxHeightSmall),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 4.0,
          children: const [
            CompanyIcon(
              onlyText: true,
              padding: 0,
              height: 16,
              spacingType: MainAxisAlignment.start,
            ),
            Text(
              '+91 -************',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.w400,
                color: AppConstants.primaryColor,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class SideBarListItem {
  final String route;
  final String title;
  final String icon;
  final List<SideBarListItem>? subItems;

  SideBarListItem({
    required this.title,
    required this.icon,
    required this.route,
    this.subItems,
  });
}
