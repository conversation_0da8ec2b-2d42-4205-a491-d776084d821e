import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_departments/get_departments_request.dart';

class GetDepartmentsRequestModel extends GetDepartmentsRequest {
  const GetDepartmentsRequestModel({required super.limit, required super.skip});

  factory GetDepartmentsRequestModel.fromEntity(GetDepartmentsRequest entity) {
    return GetDepartmentsRequestModel(limit: entity.limit, skip: entity.skip);
  }

  Map<String, dynamic> toJson() {
    return {'limit': limit, 'skip': skip};
  }
}
