import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';

class CustomSearchBar extends StatelessWidget {
  final String hintText;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Color backgroundColor;
  final Color hintColor;
  final Color iconColor;
  final BorderRadius? borderRadius;

  const CustomSearchBar({
    super.key,
    this.hintText = 'Search',
    this.onTap,
    this.onChanged,
    this.backgroundColor = AppConstants.inputFieldBackgroundColor,
    this.hintColor = AppConstants.inputFieldForegroundColor,
    this.iconColor = AppConstants.inputFieldForegroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius:
          borderRadius ??
              BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
        child: Row(
          children: [
            Icon(Icons.search, color: iconColor, size: 15),
            const SizedBox(width: AppConstants.sizedBoxHeightSmall),
            Expanded(
              child:
              onChanged != null
                  ? TextField(
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle: TextStyle(color: hintColor, fontSize: 14),
                  border: InputBorder.none,
                  isDense: true,
                  contentPadding: EdgeInsets.zero,
                ),
                onChanged: onChanged,
              )
                  : Text(
                hintText,
                style: TextStyle(color: hintColor, fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }
}