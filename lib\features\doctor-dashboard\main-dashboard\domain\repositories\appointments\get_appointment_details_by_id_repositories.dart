import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_details_by_id/get_appointments_details_by_id_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_details_by_id/get_appointments_details_by_id_response_model.dart';

abstract class GetAppointmentDetailsByIDRepositories {
  Future<Either<Failure, GetAppointmentDetailsByIDResponseModel>>
  getAppointmentDetailsById(GetAppointmentsDetailsByIDRequestModel request);
}
