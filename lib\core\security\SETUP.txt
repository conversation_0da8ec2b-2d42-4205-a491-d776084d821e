// SIMPLE TOKEN MANAGEMENT SETUP
// =============================

// What we created:
// 1. lib/core/security/token_storage.dart - Save/load tokens with flutter_secure_storage
// 2. lib/core/security/token_manager.dart - Token refresh logic
// 3. lib/core/security/auth_interceptor.dart - Auto token injection to requests
// 4. lib/core/security/auth_bloc.dart - State management (login, logout, refresh)
// 5. Updated: lib/core/network/dio_client.dart - Added interceptor support
// 6. Updated: lib/core/di/injection_container.dart - Registered dependencies
// 7. Updated: pubspec.yaml - Added flutter_secure_storage package

// SETUP STEPS:
// ============

// Step 1: Update Azure config in injection_container.dart
// sl.registerLazySingleton<TokenManager>(
//   () => TokenManager(
//     storage: sl(),
//     dio: Dio(),
//     azureClientId: 'YOUR_CLIENT_ID',      // ← Change this
//     azureClientSecret: 'YOUR_CLIENT_SECRET', // ← Change this
//     azureTokenEndpoint: 'https://login.microsoftonline.com/YOUR_TENANT_ID/oauth2/v2.0/token', // ← Change this
//   ),
// );

// Step 2: Call init() in main.dart
// void main() {
//   setupServiceLocator(); // This calls init()
//   runApp(const MyApp());
// }

// QUICK USAGE:
// ============

// Login: Save token from Azure
// context.read<AuthBloc>().add(AuthLoginEvent(tokenModel));

// Check auth on startup
// context.read<AuthBloc>().add(const AuthCheckStatusEvent());

// Use API (token auto-added by interceptor)
// await dioClient.get('/appointments');

// Manually refresh
// context.read<AuthBloc>().add(const AuthRefreshTokenEvent());

// Print token info
// context.read<AuthBloc>().add(const AuthPrintInfoEvent());

// Logout
// context.read<AuthBloc>().add(const AuthLogoutEvent());

// CONSOLE LOGS:
// =============
// All operations logged to dart:developer
// View in: Flutter DevTools > Logging tab
// Filter by: TokenStorage, TokenManager, AuthInterceptor, AuthBloc

// Example logs:
// ✅ Token saved
// ⚠️ Token expired, refreshing...
// 🔄 Refreshing token from Azure...
// ✅ Token refreshed
// → GET /appointments
// ✅ Token added to header
// ← Response 200
// ❌ Error message

// HOW IT WORKS:
// =============

// 1. User logs in with Azure AD
//    ↓
// 2. Get token from Azure (contains: access_token, refresh_token, expires_in)
//    ↓
// 3. Create TokenModel from response: TokenModel.fromAzureResponse(response)
//    ↓
// 4. Save via AuthBloc: context.read<AuthBloc>().add(AuthLoginEvent(tokenModel))
//    ↓
// 5. Tokens stored securely in device using flutter_secure_storage
//    ↓
// 6. API calls: AuthInterceptor automatically:
//    - Checks if token is expired
//    - Refreshes if needed
//    - Adds "Authorization: Bearer <token>" header
//    - Retries on 401 if refresh succeeds
//    ↓
// 7. No manual token handling needed in repositories!

// STORAGE DETAILS:
// ================
// Keys stored in flutter_secure_storage:
// - 'access_token': JWT for API calls
// - 'refresh_token': Token to refresh access token
// - 'token_expires_at': ISO 8601 expiration time

// Access directly:
// final storage = sl<TokenStorage>();
// final token = await storage.getAccessToken();
// final isExpired = await storage.isTokenExpired();
// await storage.printTokenInfo();

// FILES CREATED/UPDATED:
// ======================
// NEW:
// - lib/core/security/token_storage.dart
// - lib/core/security/token_manager.dart
// - lib/core/security/auth_interceptor.dart
// - lib/core/security/auth_bloc.dart
// - lib/core/security/auth_usage.dart (examples)

// UPDATED:
// - lib/core/network/dio_client.dart
// - lib/core/di/injection_container.dart
// - pubspec.yaml (added flutter_secure_storage: ^9.2.4)

// NO MARKDOWN FILES - Just code!
