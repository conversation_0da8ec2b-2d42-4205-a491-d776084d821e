import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/widgets/labelled-widget.dart';

class CustomInputBox extends StatefulWidget {
  final String? hintText;
  final String? labelText;
  final String? errorText;
  final String? iconPath;
  final String? regex;
  final int maxLines;
  final bool isRequired;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Color backgroundColor;
  final Color foregroundColor;
  final double borderRadius;
  final BorderSide border;
  final EdgeInsetsGeometry padding;
  final TextEditingController controller;
  final bool enabled;

  const CustomInputBox({
    super.key,
    this.labelText,
    this.hintText = "Hint text",
    this.errorText,
    this.iconPath,
    this.regex,
    this.maxLines = 1,
    this.onTap,
    this.onChanged,
    this.backgroundColor = Colors.transparent,
    this.foregroundColor = AppConstants.inputFieldForegroundColor,
    this.borderRadius = 10,
    this.isRequired = false,
    this.border = BorderSide.none,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
    required this.controller,
    this.enabled = true,
  });

  @override
  State<CustomInputBox> createState() => _CustomInputBoxState();
}

class _CustomInputBoxState extends State<CustomInputBox> {
  late String? _errorText;

  @override
  void initState() {
    super.initState();
    if (widget.controller.text.isEmpty && widget.isRequired) {
      _errorText = "This field cannot be empty";
    } else {
      _errorText = widget.errorText ?? "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return LabelledWidget(
      label: widget.labelText,
      isRequired: widget.isRequired,
      errorText: _errorText,
      border: widget.border,
      borderRadius: widget.borderRadius,
      padding: widget.padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: widget.onTap,
            child: Container(
              decoration: BoxDecoration(color: widget.backgroundColor),
              child: Row(
                children: [
                  if (widget.iconPath != null) ...[
                    ReusableSvgImage(
                      assetPath: widget.iconPath!,
                      width: 16.67,
                      height: 16.67,
                      color: AppConstants.textMidColor,
                    ),
                    const SizedBox(width: 16),
                  ],

                  Expanded(
                    child: TextField(
                      maxLines: widget.maxLines,
                      controller: widget.controller,
                      inputFormatters:
                          widget.regex != null
                              ? [
                                FilteringTextInputFormatter.allow(
                                  RegExp(widget.regex!),
                                ),
                              ]
                              : [],
                      onChanged: (value) {
                        setState(() {
                          if (widget.onChanged != null) {
                            widget.onChanged!(value);
                          }
                          if (value.isEmpty && widget.isRequired) {
                            _errorText = "This field cannot be empty";
                          } else {
                            _errorText = "";
                          }
                        });
                      },
                      enabled: widget.enabled,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        focusedErrorBorder: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                        hintText: widget.hintText,
                        hintStyle: Theme.of(
                          context,
                        ).textTheme.bodyMedium!.copyWith(
                          color:
                              widget.enabled
                                  ? widget.foregroundColor
                                  : widget.foregroundColor.withValues(
                                    alpha: 0.6,
                                  ),
                        ),
                        fillColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                      ),
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color:
                            widget.enabled
                                ? widget.foregroundColor
                                : widget.foregroundColor.withValues(alpha: 0.6),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
