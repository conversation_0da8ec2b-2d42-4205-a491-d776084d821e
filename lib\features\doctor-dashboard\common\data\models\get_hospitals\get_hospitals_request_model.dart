import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_hospitals/get_hospitals_request.dart';

class GetHospitalsRequestModel extends GetHospitalsRequest {
  const GetHospitalsRequestModel({required super.limit, required super.skip});

  factory GetHospitalsRequestModel.fromEntity(GetHospitalsRequest entity) {
    return GetHospitalsRequestModel(limit: entity.limit, skip: entity.skip);
  }

  Map<String, dynamic> toJson() {
    return {'limit': limit, 'skip': skip};
  }
}
