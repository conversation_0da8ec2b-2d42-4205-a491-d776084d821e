import 'dart:convert';
import 'package:imed_fe/features/doctor-dashboard/availability/data/models/doctor_availability_schedule_model.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/domain/entities/doctor_availability_schedule.dart';

void main() {
  // Create a sample availability schedule like the UI would
  final schedule = DoctorAvailabilityScheduleModel(
    startDate: DateTime(2024, 12, 5),
    endDate: DateTime(2025, 1, 4),
    isRepeat: false,
    unavailableDates: [],
    days: [
      DayScheduleModel(
        day: 'monday',
        isOffDay: false,
        slots: [
          AvailabilityTimeSlotModel(
            startTime: '09:00',
            endTime: '17:00',
          ),
        ],
      ),
      DayScheduleModel(
        day: 'tuesday',
        isOffDay: true,
        slots: [],
      ),
    ],
  );

  // Print the JSON that would be sent to the API
  final json = schedule.toJson();
  final prettyJson = const JsonEncoder.withIndent('  ').convert(json);
  
  print('🔍 DEBUG: Request body that would be sent to API:');
  print(prettyJson);
  
  print('\n📋 Key Information:');
  print('🌐 Endpoint: http://10.10.1.5:3000/doctors/availability');
  print('📋 Method: POST');
  print('📤 Content-Type: application/json');
  print('🔑 Auth: Bearer token (check if properly formatted)');
}
