import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/appointment/get_appointments_by_doctor_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_by_doctor/get_appointments_by_doctor_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_by_doctor/get_appointments_by_doctor_response_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointments_by_doctor_repositories.dart';

class GetAppointmentByDoctorRepositoriesImpl
    implements GetAppointmentByDoctorRepositories {
  final GetAppointmentsByDoctorRemoteDatasource remoteDatasource;

  GetAppointmentByDoctorRepositoriesImpl({required this.remoteDatasource});

  @override
  Future<Either<Failure, GetAppointmentsByDoctorResponseModel>>
  getAppointmentsByDoctor(GetAppointmentsByDoctorRequestModel request) async {
    try {
      final response = await remoteDatasource.getAppointmentsByDoctor(request);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
