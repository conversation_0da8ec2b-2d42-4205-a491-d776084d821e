import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointments_by_doctor/get_appointments_by_doctor_request.dart';

class GetAppointmentsByDoctorRequestModel
    extends GetAppointmentsByDoctorRequest {
  const GetAppointmentsByDoctorRequestModel({
    super.appointmentType,
    super.source,
    super.startDate,
    super.endDate,
    super.month,
    super.year,
    super.status,
    required super.skip,
    required super.limit,
  });

  factory GetAppointmentsByDoctorRequestModel.fromEntity(
    GetAppointmentsByDoctorRequest entity,
  ) {
    return GetAppointmentsByDoctorRequestModel(
      appointmentType: entity.appointmentType,
      source: entity.source,
      startDate: entity.startDate,
      endDate: entity.endDate,
      month: entity.month,
      year: entity.year,
      status: entity.status,
      skip: entity.skip,
      limit: entity.limit,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "appointmentType": appointmentType,
      "source": source,
      "start_date": startDate,
      "end_date": endDate,
      "month": month,
      "year": year,
      "status": status,
      "skip": skip,
      "limit": limit,
    };
  }
}
