part of 'appointment_bloc.dart';

@immutable
abstract class AppointmentState {}

class AppointmentInitial extends AppointmentState {}

class AppointmentLoading extends AppointmentState {}

class AppointmentEmpty extends AppointmentState {}

class AppointmentError extends AppointmentState {
  final String message;
  AppointmentError(this.message);
}


class AppointmentLoaded extends AppointmentState {
  final List<AppointmentEntity> appointments;
  AppointmentLoaded(this.appointments);
}

class AppointmentOperationLoading extends AppointmentState {
  final List<AppointmentEntity> currentAppointments;
  AppointmentOperationLoading(this.currentAppointments);
}

class AppointmentOperationSuccess extends AppointmentState {
  final String message;
  final List<AppointmentEntity> appointments;
  AppointmentOperationSuccess({
    required this.message,
    required this.appointments,
  });
}


class AppointmentDetailLoading extends AppointmentState {}

class AppointmentDetailLoaded extends AppointmentState {
  final AppointmentDetail appointmentDetail;
  AppointmentDetailLoaded(this.appointmentDetail);
}

class NewAppointmentLoading extends AppointmentState {}

class NewAppointmentSuccess extends AppointmentState {
  final dynamic appointment;
  NewAppointmentSuccess(this.appointment);
}

class NewAppointmentFailure extends AppointmentState {
  final String error;
  NewAppointmentFailure(this.error);
}