import '../../data/models/appointment_model.dart';

class AppointmentEntity {
  final String appointmentId;
  final String time;
  final String bookingId;
  final String patientId;
  final String patientName;
  final String phone;
  final String gender;
  final String status;

  AppointmentEntity({
    required this.appointmentId,
    required this.time,
    required this.bookingId,
    required this.patientId,
    required this.patientName,
    required this.phone,
    required this.gender,
    required this.status,
  });
  factory AppointmentEntity.empty() {
    return AppointmentEntity(
      appointmentId: '',
      patientName: '',
      time: '',
      phone: '',
      gender: '',
      status: '',
      bookingId: '',
      patientId: '',
    );
  }

  // Convert from model to entity
  factory AppointmentEntity.fromModel(AppointmentModel model) {
    return AppointmentEntity(
      appointmentId: model.appointmentId,
      time: model.time,
      bookingId: model.bookingId,
      patientId: model.patientId,
      patientName: model.patientName,
      phone: model.phone,
      gender: model.gender,
      status: model.status,
    );
  }
}