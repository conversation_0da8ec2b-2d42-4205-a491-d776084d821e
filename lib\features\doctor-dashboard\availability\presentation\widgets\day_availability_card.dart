import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'time_slot_row.dart';
import '../bloc/availability_event.dart' show TimeSlotData;

class DayAvailabilityCard extends StatelessWidget {
  final String dayName;
  final bool isActive;
  final List<TimeSlotData> timeSlots;
  final ValueChanged<bool> onToggle;
  final VoidCallback onAddSlot;
  final Future<void> Function(TimeSlotData, bool) onTimeSelect;
  final void Function(TimeSlotData) onDeleteSlot;

  const DayAvailabilityCard({
    super.key,
    required this.dayName,
    required this.isActive,
    required this.timeSlots,
    required this.onToggle,
    required this.onAddSlot,
    required this.onTimeSelect,
    required this.onDeleteSlot,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppConstants.primaryColor.withValues(alpha: 0.09),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: AppConstants.inputFieldForegroundColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Switch(
                    value: isActive,
                    onChanged: onToggle,
                    activeTrackColor: AppConstants.primaryColor,
                    activeThumbColor: Colors.white,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    dayName,
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: FontWeight.w600,
                      color:
                          isActive
                              ? AppConstants.textPrimaryColor
                              : AppConstants.textMidColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (isActive) ...[
            const SizedBox(height: 16),
            // Time slots
            ...timeSlots.map(
              (slot) => TimeSlotRow(
                slot: slot,
                onTimeSelect: onTimeSelect,
                onDelete: onDeleteSlot,
              ),
            ),
            const SizedBox(height: 16),
            // Add Another Slot button
            GestureDetector(
              onTap: onAddSlot,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppConstants.primaryColor,
                    style: BorderStyle.solid,
                  ),
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadiusMedium,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add, color: AppConstants.primaryColor, size: 20),
                    const SizedBox(width: 16),
                    Text(
                      'Add Another Slot',
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
