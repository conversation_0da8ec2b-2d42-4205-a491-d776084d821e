import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_data.dart';

class UpdateAppointmentStatusByAppointmentidResponse extends Equatable {
  final UpdateAppointmentStatusByAppointmentidData data;
  final String message;
  final String status;
  final int statusCode;

  const UpdateAppointmentStatusByAppointmentidResponse({
    required this.data,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  @override
  List<Object?> get props => [data, message, status, statusCode];
}
