import 'package:flutter/material.dart';

class ReusableAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? leading;
  final Widget title;
  final Widget? trailing;
  final double leadingWidth;
  final VoidCallback? onLeadingTap; // Added onLeadingTap callback
  final bool centerTitle; // Added centerTitle parameter

  const ReusableAppBar({
    super.key,
    this.leading,
    required this.title,
    this.trailing,
    this.leadingWidth = 30.0,
    this.onLeadingTap, // Added to constructor
    this.centerTitle = false, // Default to false (not centered)
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: leading != null
          ? GestureDetector(
              onTap: onLeadingTap,
              child: leading!,
            )
          : null,
      leadingWidth: leadingWidth,
      title: title,
      centerTitle: centerTitle,
      actions: trailing != null ? [trailing!] : null,
      backgroundColor: Colors.transparent,
      elevation: 0,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
