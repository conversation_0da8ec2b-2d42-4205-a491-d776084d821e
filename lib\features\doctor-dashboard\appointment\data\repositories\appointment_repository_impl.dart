import '../models/appointment_model.dart';
import 'appointment_repository.dart';
import '../models/appointment_detail_model.dart';
import '../models/new_appointment_model.dart';
import '../../domain/entities/appointment_entity.dart';
import '../../domain/entities/new_appointment_request.dart';

class AppointmentRepositoryImpl implements AppointmentRepository {
  @override
  Future<List<AppointmentEntity>> getAppointments() async {
    // Simulate API delay
    await Future.delayed(const Duration(seconds: 2));

    // Dummy API response matching your structure
    final dummyResponse = {
      "data": [
        {
          "appointment_id": "d0eb7569-9ad7-4fc5-b51a-fc82c99e5338",
          "time": "20:30:00 - 21:00:00",
          "booking_id": "WALKIN-1759743828703-JA6GJEQBB",
          "patient_id": "8e798426-6e2c-4d9c-acac-c3fdbbecef1b",
          "patient_name": "<PERSON>",
          "phone": "01712345678",
          "gender": "male",
          "status": "confirmed"
        },
        {
          "appointment_id": "5117f54b-638e-46dc-8794-8020c96dda5b",
          "time": "19:00:00 - 19:30:00",
          "booking_id": "WALKIN-KLCSMM45",
          "patient_id": "8e798426-6e2c-4d9c-acac-c3fdbbecef1b",
          "patient_name": "John Doe",
          "phone": "01712345678",
          "gender": "male",
          "status": "confirmed"
        }
      ],
      "message": "Appointments fetched successfully",
      "status": "success",
      "statusCode": 200
    };

    final response = AppointmentsResponseModel.fromJson(dummyResponse);
    return response.data.map((model) => _mapModelToEntity(model)).toList();
  }

  AppointmentEntity _mapModelToEntity(AppointmentModel model) {
    return AppointmentEntity(
      appointmentId: model.appointmentId,
      patientName: model.patientName,
      time: model.time,
      phone: model.phone,
      gender: model.gender,
      status: model.status,
      bookingId: model.bookingId,
      patientId: '',
    );
  }

  @override
  Future<void> cancelAppointment(String appointmentId) async {
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    print('Appointment $appointmentId cancelled');
  }

  @override
  Future<void> confirmAppointment(String appointmentId) async {
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    print('Appointment $appointmentId confirmed');
  }

  @override
  Future<AppointmentDetail> getAppointmentDetail(String appointmentId) async {
    // Simulate API delay
    await Future.delayed(const Duration(seconds: 1));

    // Dummy appointment detail response
    final dummyDetailResponse = {
      "data": {
        "appointment_id": appointmentId,
        "patient_name": "John Doe",
        "age": 35,
        "patient_phone": "01712345678",
        "appointment_type": "Consultation",
        "complaints": [
          {"label": "Headache", "value": "Severe"},
          {"label": "Fever", "value": "Mild"}
        ],
        "status": "confirmed",
        "reason": "Regular checkup",
        "date": "2024-01-15",
        "start_time": "20:30:00",
        "end_time": "21:00:00",
        "files": [
          {"file_name": "medical_report.pdf"}
        ]
      },
      "message": "Appointment detail fetched successfully",
      "status": "success",
      "statusCode": 200
    };

    // Create the model from JSON, then convert to domain entity
    final model = AppointmentDetailModel.fromJson(dummyDetailResponse);
    return model.toEntity(); // This calls toEntity() on the MODEL, not the entity
  }

  @override
  Future<NewAppointmentModel> createAppointment(NewAppointmentRequest request) async {
    // Simulate API delay
    await Future.delayed(const Duration(seconds: 2));

    // Dummy new appointment response
    final dummyResponse = {
      "data": {
        "appointment_id": "new-${DateTime.now().millisecondsSinceEpoch}",
        "booking_id": "WALKIN-${DateTime.now().millisecondsSinceEpoch}",
        "patient_name": "${request.firstName} ${request.lastName}",
        "status": "scheduled",
        "date": request.date,
        "time": request.timeSlot,
      },
      "message": "Appointment created successfully",
      "status": "success",
      "statusCode": 201
    };

    return NewAppointmentModel.fromJson(dummyResponse);
  }
}