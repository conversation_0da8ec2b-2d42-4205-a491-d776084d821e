import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'labelled_widget.dart';

class CustomComboBox extends StatefulWidget {
  final String? hintText;
  final String? labelText;
  final String? iconPath;
  final bool isRequired;
  final Function(int)? onChanged;
  final Color backgroundColor;
  final Color foregroundColor;
  final double borderRadius;
  final BorderSide border;
  final EdgeInsetsGeometry padding;
  final List<DropdownMenuItem<String>> items;
  final bool enabled;
  final int selectedIndex;

  const CustomComboBox({
    super.key,
    this.labelText,
    this.hintText = "Hint text",
    this.items = const [],
    this.iconPath,
    this.isRequired = false,
    this.onChanged,
    this.backgroundColor = Colors.transparent,
    this.foregroundColor = AppConstants.inputFieldForegroundColor,
    this.borderRadius = 10,
    this.border = BorderSide.none,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
    this.enabled = true,
    this.selectedIndex = 0,
  });

  @override
  State<CustomComboBox> createState() => _CustomComboBoxState();
}

class _CustomComboBoxState extends State<CustomComboBox> {
  late int _selectedIndex;

  String errorText = "";
  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.selectedIndex;
    if (widget.items.isEmpty) {
      errorText = "Please select an option";
    } else if (widget.isRequired &&
        (_selectedIndex < 0 || _selectedIndex >= widget.items.length)) {
      errorText = "This field cannot be empty";
    } else {
      errorText = "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LabelledWidget(
          label: widget.labelText,
          isRequired: widget.isRequired,
          border: widget.border,
          errorText: errorText,
          borderRadius: widget.borderRadius,
          padding: widget.padding,
          child: Container(
            decoration: BoxDecoration(
              color: widget.backgroundColor,
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: Row(
              children: [
                if (widget.iconPath != null) ...[
                  ReusableSvgImage(
                    assetPath: widget.iconPath!,
                    width: 16.67,
                    height: 16.67,
                  ),
                  const SizedBox(width: 16),
                ],
                Expanded(
                  child: SizedBox(
                    height: 48,
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        isExpanded: true,
                        hint: Text(
                          widget.hintText ?? '',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            color: Color(0xFF718DE0),
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        value:
                            (_selectedIndex >= 0 &&
                                    _selectedIndex < widget.items.length)
                                ? widget.items[_selectedIndex].value
                                : null,
                        items: widget.items,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color:
                              widget.enabled
                                  ? widget.foregroundColor
                                  : widget.foregroundColor.withValues(
                                    alpha: 0.6,
                                  ),
                        ),
                        onChanged:
                            widget.enabled
                                ? (value) {
                                  if (value != null) {
                                    final newIndex = widget.items.indexWhere(
                                      (item) => item.value == value,
                                    );
                                    if (newIndex != -1) {
                                      setState(() {
                                        _selectedIndex = newIndex;
                                        errorText = "";
                                      });
                                      if (widget.onChanged != null) {
                                        widget.onChanged!(newIndex);
                                      }
                                    }
                                  }
                                  FocusScope.of(
                                    context,
                                  ).requestFocus(FocusNode());
                                }
                                : null,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
