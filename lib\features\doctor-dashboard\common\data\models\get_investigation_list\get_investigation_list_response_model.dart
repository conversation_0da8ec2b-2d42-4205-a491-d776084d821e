import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_investigation_list/get_investigation_list_data.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_investigation_list/get_investigation_list_response.dart';

class GetInvestigationListResponseModel extends GetInvestigationListResponse {
  const GetInvestigationListResponseModel({
    required super.data,
    required super.total,
    required super.skip,
    required super.limit,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory GetInvestigationListResponseModel.fromJson(
    Map<String, dynamic> json,
  ) {
    final data = json['data'] as List<dynamic>?;

    final methods =
        data
            ?.map(
              (e) => Investigation(
                name: (e['name'] as String?) ?? '',
                value: (e['value'] as String?) ?? '',
                description: (e['description'] as String?) ?? '',
              ),
            )
            .toList() ??
        <Investigation>[];

    return GetInvestigationListResponseModel(
      data: GetInvestigationListData(data: methods),
      total: json['total'] as int? ?? 0,
      skip: json['skip'] as String,
      limit: json['limit'] as String,
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '',
      statusCode: json['statusCode'] as int? ?? 0,
    );
  }

  factory GetInvestigationListResponseModel.fromEntity(
    GetInvestigationListResponse entity,
  ) {
    return GetInvestigationListResponseModel(
      data: entity.data,
      total: entity.total,
      skip: entity.skip,
      limit: entity.limit,
      message: entity.message ?? '',
      status: entity.status ?? '',
      statusCode: entity.statusCode ?? 0,
    );
  }
}
