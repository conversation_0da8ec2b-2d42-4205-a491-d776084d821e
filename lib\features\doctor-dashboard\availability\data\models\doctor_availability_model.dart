import '../../domain/entities/doctor_availability.dart';
import '../../domain/entities/time_slot.dart';
import 'time_slot_model.dart';

class DoctorAvailabilityModel extends DoctorAvailability {
  const DoctorAvailabilityModel({
    required super.doctorId,
    required super.doctorName,
    required super.date,
    required super.timeSlots,
    required super.isAvailable,
  });

  factory DoctorAvailabilityModel.fromJson(Map<String, dynamic> json) {
    return DoctorAvailabilityModel(
      doctorId: json['doctor_id'] as String,
      doctorName: json['doctor_name'] as String,
      date: DateTime.parse(json['date'] as String),
      timeSlots: (json['time_slots'] as List<dynamic>)
          .map((slot) => TimeSlotModel.fromJson(slot as Map<String, dynamic>))
          .toList(),
      isAvailable: json['is_available'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'doctor_id': doctorId,
      'doctor_name': doctor<PERSON><PERSON>,
      'date': date.toIso8601String(),
      'time_slots': timeSlots
          .map((slot) => TimeSlotModel.fromEntity(slot).toJson())
          .toList(),
      'is_available': isAvailable,
    };
  }

  factory DoctorAvailabilityModel.fromEntity(DoctorAvailability availability) {
    return DoctorAvailabilityModel(
      doctorId: availability.doctorId,
      doctorName: availability.doctorName,
      date: availability.date,
      timeSlots: availability.timeSlots,
      isAvailable: availability.isAvailable,
    );
  }

  DoctorAvailability toEntity() {
    return DoctorAvailability(
      doctorId: doctorId,
      doctorName: doctorName,
      date: date,
      timeSlots: timeSlots,
      isAvailable: isAvailable,
    );
  }
}
