import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/network/network_info.dart';
import '../../domain/entities/appointment.dart';
import '../../domain/entities/doctor_availability.dart';
import '../../domain/entities/doctor_availability_schedule.dart';
import '../../domain/entities/time_slot.dart';
import '../../domain/repositories/availability_repository.dart';
import '../datasources/availability_remote_data_source.dart';
import '../models/appointment_model.dart';
import '../models/doctor_availability_schedule_model.dart';

class AvailabilityRepositoryImpl implements AvailabilityRepository {
  final AvailabilityRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  AvailabilityRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, Appointment>> createAppointment(
    Appointment appointment,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final appointmentModel = AppointmentModel.fromEntity(appointment);
        final result = await remoteDataSource.createAppointment(
          appointmentModel,
        );
        return Right(result.toEntity());
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      } on NetworkException catch (e) {
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, DoctorAvailability>> getDoctorAvailability({
    required String doctorId,
    required DateTime date,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getDoctorAvailability(
          doctorId: doctorId,
          date: date,
        );
        return Right(result.toEntity());
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      } on NetworkException catch (e) {
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<TimeSlot>>> getAvailableTimeSlots({
    required String doctorId,
    required DateTime date,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getAvailableTimeSlots(
          doctorId: doctorId,
          date: date,
        );
        return Right(result.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      } on NetworkException catch (e) {
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<Appointment>>> getPatientAppointments({
    required String patientId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getPatientAppointments(
          patientId: patientId,
        );
        return Right(result.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      } on NetworkException catch (e) {
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<Appointment>>> getDoctorAppointments({
    required String doctorId,
    DateTime? date,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getDoctorAppointments(
          doctorId: doctorId,
          date: date,
        );
        return Right(result.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      } on NetworkException catch (e) {
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, Appointment>> updateAppointmentStatus({
    required String appointmentId,
    required String status,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.updateAppointmentStatus(
          appointmentId: appointmentId,
          status: status,
        );
        return Right(result.toEntity());
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      } on NetworkException catch (e) {
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> cancelAppointment({
    required String appointmentId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.cancelAppointment(
          appointmentId: appointmentId,
        );
        return Right(result);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      } on NetworkException catch (e) {
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, DoctorAvailabilitySchedule>>
  setDoctorAvailabilitySchedule(DoctorAvailabilitySchedule schedule) async {
    if (await networkInfo.isConnected) {
      try {
        final scheduleModel = DoctorAvailabilityScheduleModel.fromEntity(
          schedule,
        );
        final result = await remoteDataSource.setDoctorAvailability(
          scheduleModel,
        );
        return Right(result.toEntity());
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      } on NetworkException catch (e) {
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return const Left(NetworkFailure());
    }
  }
}
