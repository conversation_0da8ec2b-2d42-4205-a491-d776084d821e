class AppointmentModel {
  final String appointmentId;
  final String time;
  final String bookingId;
  final String patientId;
  final String patientName;
  final String phone;
  final String gender;
  final String status;

  AppointmentModel({
    required this.appointmentId,
    required this.time,
    required this.bookingId,
    required this.patientId,
    required this.patientName,
    required this.phone,
    required this.gender,
    required this.status,
  });

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    return AppointmentModel(
      appointmentId: json['appointment_id'] ?? '',
      time: json['time'] ?? '',
      bookingId: json['booking_id'] ?? '',
      patientId: json['patient_id'] ?? '',
      patientName: json['patient_name'] ?? '',
      phone: json['phone'] ?? '',
      gender: json['gender'] ?? '',
      status: json['status'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'appointment_id': appointmentId,
      'time': time,
      'booking_id': bookingId,
      'patient_id': patientId,
      'patient_name': patientName,
      'phone': phone,
      'gender': gender,
      'status': status,
    };
  }
}

class AppointmentsResponseModel {
  final List<AppointmentModel> data;
  final String message;
  final String status;
  final int statusCode;

  AppointmentsResponseModel({
    required this.data,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  factory AppointmentsResponseModel.fromJson(Map<String, dynamic> json) {
    return AppointmentsResponseModel(
      data: (json['data'] as List<dynamic>)
          .map((item) => AppointmentModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      message: json['message'] ?? '',
      status: json['status'] ?? '',
      statusCode: json['statusCode'] ?? 0,
    );
  }
}