import 'package:flutter/material.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class ReusableIconButton extends StatelessWidget {
  final String? iconPath;
  final IconData? icon;
  final VoidCallback onPressed;
  final Color? color;
  final double? size;

  const ReusableIconButton({
    super.key,
    this.iconPath,
    this.icon,
    required this.onPressed,
    this.color,
    this.size,
  }) : assert(
         iconPath != null || icon != null,
         'Either iconPath or icon must be provided',
       );

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed,
      icon:
          iconPath != null
              ? ReusableSvgImage(
                assetPath: iconPath!,
                color: color,
                height: size,
                width: size,
              )
              : Icon(icon, color: color, size: size),
    );
  }
}
