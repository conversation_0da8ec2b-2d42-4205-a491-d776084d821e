import 'package:flutter/material.dart';

class CustomOutlinedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String text;
  final Color? borderColor;
  final Color? foregroundColor;
  final double? borderRadius;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Widget? prefixIcon;
  final Widget? postfixIcon;
  final double? width;

  const CustomOutlinedButton({
    super.key,
    this.onPressed,
    required this.text,
    this.borderColor,
    this.foregroundColor,
    this.borderRadius,
    this.fontSize,
    this.fontWeight,
    this.prefixIcon,
    this.postfixIcon,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final buttonChild = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (prefixIcon != null) ...[prefixIcon!, const SizedBox(width: 8)],
        Text(
          text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: fontWeight,
            color: foregroundColor,
          ),
        ),
        if (postfixIcon != null) ...[const SizedBox(width: 8), postfixIcon!],
      ],
    );

    return SizedBox(
      width: width,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          foregroundColor: foregroundColor,
          side: borderColor != null ? BorderSide(color: borderColor!) : null,
          shape:
              borderRadius != null
                  ? RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(borderRadius!),
                  )
                  : null,
        ),
        child: buttonChild,
      ),
    );
  }
}
