import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_designation/get_designation_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_designation/get_designation_request.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_designation/get_designation_response.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_designations_repositories.dart';

class GetDesignations
    implements UseCase<GetDesignationResponse, GetDesignationRequest> {
  final GetDesignationsRepositories repository;

  GetDesignations(this.repository);

  @override
  Future<Either<Failure, GetDesignationResponse>> call(
    GetDesignationRequest params,
  ) async {
    final requestModel = GetDesignationsRequestModel.fromEntity(params);
    return await repository.getAllDesignations(requestModel);
  }
}
