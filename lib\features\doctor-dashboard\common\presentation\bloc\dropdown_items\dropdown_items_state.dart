import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_all_address_type/get_all_address_type_data.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_departments/get_departments_data.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_designation/get_designation_data.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_hospitals/get_hospitals_data.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_investigation_list/get_investigation_list_data.dart';

abstract class DropDownItemsState extends Equatable {
  @override
  List<Object?> get props => [];
}

class DropDownItemsInitial extends DropDownItemsState {}

class DropDownItemsLoading extends DropDownItemsState {}

class DropDownItemsLoaded extends DropDownItemsState {
  final GetAllAddressTypeData addressData;
  final GetDepartmentsData departmentData;
  final GetDesignationData designationData;
  final GetHospitalsData hospitalsData;
  final GetInvestigationListData investigationListData;
  DropDownItemsLoaded({
    required this.addressData,
    required this.departmentData,
    required this.designationData,
    required this.hospitalsData,
    required this.investigationListData,
  });

  @override
  List<Object?> get props => [
    addressData,
    departmentData,
    designationData,
    hospitalsData,
    investigationListData,
  ];
}

class DropDownItemsError extends DropDownItemsState {
  final String message;
  final String? status;

  DropDownItemsError({required this.message, this.status});

  @override
  List<Object?> get props => [message, status];
}
