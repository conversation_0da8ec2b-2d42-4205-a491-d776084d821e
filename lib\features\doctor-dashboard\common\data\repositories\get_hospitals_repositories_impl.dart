import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_hospitals_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_hospitals/get_hospitals_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_hospitals/get_hospitals_response_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_hospitals_repositories.dart';

class GetHospitalsRepositoriesImpl implements GetHospitalsRepositories {
  final GetHospitalsRemoteDatasource remoteDatasource;

  GetHospitalsRepositoriesImpl({required this.remoteDatasource});

  @override
  Future<Either<Failure, GetHospitalsResponseModel>> getAllHospitals(
    GetHospitalsRequestModel requestModel,
  ) async {
    try {
      final response = await remoteDatasource.getHospitals(requestModel);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
