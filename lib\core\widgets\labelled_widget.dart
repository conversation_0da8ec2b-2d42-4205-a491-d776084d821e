import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';

class LabelledWidget extends StatelessWidget {
  final String? label;
  final String? errorText;
  final Color labelColor;
  final Widget child;
  final bool isRequired;
  final BorderSide? border;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  const LabelledWidget({
    super.key,
    this.label,
    this.errorText,
    required this.child,
    this.isRequired = false,
    this.labelColor = AppConstants.textMidColor,
    this.border,
    this.borderRadius = 10,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: AppConstants.sizedBoxHeightSmall,
      children: [
        if (label != null && label!.isNotEmpty) ...[
          Row(
            children: [
              Text(
                label!,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: AppConstants.fontSize14,
                  color: labelColor,
                ),
              ),

              SizedBox(width: AppConstants.sizedBoxHeightSmall),
              if (isRequired)
                Text(
                  "*",
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize: AppConstants.fontSize14,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFFE62F2F),
                  ),
                ),
            ],
          ),
          if (errorText != null && errorText!.isNotEmpty) ...[
            Text(
              errorText!,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                fontSize: AppConstants.fontSizeSmall,
                color: Color(0xFFE62F2F),
              ),
            ),
          ],
        ],
        Container(
          padding: padding,
          decoration: BoxDecoration(
            border:
            (errorText != null && errorText!.isNotEmpty)
                ? Border.all(color: Color(0xFFE62F2F))
                : border != null
                ? border == BorderSide.none
                ? null
                : Border.fromBorderSide(border!)
                : Border.all(color: AppConstants.borderColor),
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: child,
        ),
      ],
    );
  }
}