import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_designation/get_designation_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_designation/get_designation_response_model.dart';

abstract class GetDesignationsRemoteDatasource {
  Future<GetDesignationsResponseModel> getAllDesignations(
    GetDesignationsRequestModel requestModel,
  );
}

class GetDesignationsRemoteDatasourceImpl
    implements GetDesignationsRemoteDatasource {
  final DioClient dioClient;

  GetDesignationsRemoteDatasourceImpl({required this.dioClient});

  @override
  Future<GetDesignationsResponseModel> getAllDesignations(
    GetDesignationsRequestModel request,
  ) async {
    try {
      // final queryParams = request.toJson();

      // final response = await dioClient.get(
      //   '{{devURL}}/doctors/designations?skip=${request.skip}&limit=${request.limit}',
      // );
      // return GetDesignationsResponseModel.fromJson(response);

      await Future.delayed(const Duration(seconds: 2));

      final responseJson = {
        "data": [
          {
            "value": "4e839d79-fb7d-423f-aee8-c409dewwew23",
            "label": "Consultant",
          },
          {
            "value": "4e839d79-fb7d-423f-aee8-c409d38236a6",
            "label": "Senior Medical Officer",
          },
          {
            "value": "3fe9c2e5-2a3f-4414-8509-a4b7e1467f6a",
            "label": "Assistant Medical Officer",
          },
        ],
        "message": "Doctor designations fetched successfully",
        "status": "success",
        "statusCode": 200,
      };

      return GetDesignationsResponseModel.fromJson(responseJson);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
