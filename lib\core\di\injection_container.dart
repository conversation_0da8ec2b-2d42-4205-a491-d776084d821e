import 'package:get_it/get_it.dart';
import 'package:imed_fe/features/doctor-dashboard/appointment/data/repositories/appointment_repository.dart';
import 'package:imed_fe/features/doctor-dashboard/appointment/data/repositories/appointment_repository_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/appointment/domain/usecases/cancel_appointment_usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/appointment/domain/usecases/confirm_appointment_usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/appointment/domain/usecases/get_appointment_detail_usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/appointment/domain/usecases/get_appointment_usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/appointment/domain/usecases/new_appointment_usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/appointment/presentation/bloc/appointment_bloc.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/di/availability_injection.dart';

import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_all_address_type_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_departments_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_designation_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_hospitals_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_investigation_list_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_all_address_type_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_departments_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_designations_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_hospitals_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_investigation_list_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_all_address_type_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_departments_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_designations_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_hospitals_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_investigation_list_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_all_address_type.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_departments.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_designations.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_hospitals.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_investigation_list.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/bloc/dropdown_items/dropdown_items_bloc.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/appointment/get_appointment_details_by_id_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/appointment/get_appointments_by_doctor_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/appointment/update_appointment_status_by_appointmentid_remote_datasource.dart';

import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/repositories/appointments/get_appointment_details_by_id_repositries_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/repositories/appointments/get_appointments_by_doctor_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/repositories/appointments/update_appointment_status_by_appointmentid_repositories_impl.dart';

import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointment_details_by_id_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointments_by_doctor_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/update_appointment_status_by_appointmentid_repositories.dart';

import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/usecases/appointment/get_appointment_details_by_id.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/usecases/appointment/get_appointments_by_doctor.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/usecases/appointment/update_appointment_status_by_appointmentid.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/dashboard/dashboard_bloc.dart';

import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/core/network/network_info.dart';
import 'package:imed_fe/core/network/network_info_impl.dart';
import 'package:imed_fe/core/security/storage/token_storage.dart';
import 'package:imed_fe/core/security/services/token_service.dart';
import 'package:imed_fe/core/security/bloc/auth_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:dio/dio.dart' show Dio;

final sl = GetIt.instance;

Future<void> init() async {
  // Auth & Token Management
  sl.registerSingleton<FlutterSecureStorage>(const FlutterSecureStorage());

  sl.registerLazySingleton<TokenStorage>(
    () => TokenStorage(sl<FlutterSecureStorage>()),
  );

  sl.registerLazySingleton<TokenService>(
    () => TokenService(
      storage: sl(),
      dio: Dio(),
      azureClientId: 'YOUR_CLIENT_ID',
      azureClientSecret: 'YOUR_CLIENT_SECRET',
      azureTokenEndpoint:
          'https://login.microsoftonline.com/YOUR_TENANT_ID/oauth2/v2.0/token',
    ),
  );

  sl.registerFactory<AuthBloc>(() => AuthBloc(sl()));

  //Avilability Feature
  await injectAvailability(sl);

  // Blocs
  sl.registerFactory(
    () => DashboardBloc(
      getAppointmentbyDoctorRepositories: sl(),
      getAppointmentDetailsByIDRepositories: sl(),
      updateAppointmentStatusByAppointmentidRepositories: sl(),
    ),
  );

  sl.registerFactory(
    () => DropdownItemsBloc(
      getAllAddressTypeRemoteDatasource: sl(),
      getDepartmentsRemoteDatasource: sl(),
      getDesignationsRemoteDatasource: sl(),
      getHospitalsRemoteDatasource: sl(),
      getInvestigationListRemoteDatasource: sl(),
    ),
  );

  // Appointment Feature - Blocs
  sl.registerFactory(
    () => AppointmentBloc(
      getAppointmentsUseCase: sl(),
      cancelAppointmentUseCase: sl(),
      confirmAppointmentUseCase: sl(),
      getAppointmentDetailUsecase: sl(),
      newAppointmentUsecase: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetAppointmentsByDoctor(sl()));
  sl.registerLazySingleton(() => GetAppointmentDetailsById(sl()));
  sl.registerLazySingleton(() => UpdateAppointmentStatusByAppointmentid(sl()));
  sl.registerLazySingleton(() => GetAllAddressType(sl()));
  sl.registerLazySingleton(() => GetDepartments(sl()));
  sl.registerLazySingleton(() => GetDesignations(sl()));
  sl.registerLazySingleton(() => GetHospitals(sl()));

  // Availability Feature - Use cases are registered in availability_injection.dart

  // Appointment Feature -  Use Cases
  sl.registerLazySingleton(() => GetAppointmentsUseCase(sl()));
  sl.registerLazySingleton(() => CancelAppointmentUseCase(sl()));
  sl.registerLazySingleton(() => ConfirmAppointmentUseCase(sl()));
  sl.registerLazySingleton(() => GetAppointmentDetailUsecase(sl()));
  sl.registerLazySingleton(() => NewAppointmentUsecase(sl()));

  // Repositories

  sl.registerLazySingleton<GetAppointmentByDoctorRepositories>(
    () => GetAppointmentByDoctorRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<GetAppointmentDetailsByIDRepositories>(
    () => GetAppointmentDetailsByIDRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<UpdateAppointmentStatusByAppointmentidRepositories>(
    () => UpdateAppointmentStatusByAppointmentidRepositoriesImpl(
      remoteDatasource: sl(),
    ),
  );

  sl.registerLazySingleton<GetAllAddressTypeRepositories>(
    () => GetAllAddressTypeRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<GetDepartmentsRepositories>(
    () => GetDepartmentsRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<GetDesignationsRepositories>(
    () => GetDesignationsRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<GetHospitalsRepositories>(
    () => GetHospitalsRepositoriesImpl(remoteDatasource: sl()),
  );

  // Appointment Feature - Repositories
  sl.registerLazySingleton<AppointmentRepository>(
    () => AppointmentRepositoryImpl(),
  );

  // Invoice & Payment Feature - Repositories

  // Data sources
  sl.registerLazySingleton<GetAppointmentsByDoctorRemoteDatasource>(
    () => GetAppointmentsByDoctorRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<GetAppointmentDetailsByIDRemoteDatasource>(
    () => GetAppointmentDetailsByIDRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<
    UpdateAppointmentStatusByAppointmentidRemoteDatasource
  >(
    () => UpdateAppointmentStatusByAppointmentidRemoteDatasourceImpl(
      dioClient: sl(),
    ),
  );

  sl.registerLazySingleton<GetAllAddressTypeRemoteDatasource>(
    () => GetAllAddressTypeRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<GetDepartmentsRemoteDatasource>(
    () => GetDepartmentsRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<GetDesignationsRemoteDatasource>(
    () => GetDesignationsRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<GetHospitalsRemoteDatasource>(
    () => GetHospitalsRemoteDatasourceImpl(dioClient: sl()),
  );

  // Invoice & Payment Feature - Data sources

  // Core
  sl.registerLazySingleton(() => DioClient());
  sl.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(InternetConnection()),
  );

  // Initialize token service with dio client
  Future.microtask(() {
    final dioClient = sl<DioClient>();
    final tokenService = sl<TokenService>();
    dioClient.setTokenManager(tokenService);
  });
}
