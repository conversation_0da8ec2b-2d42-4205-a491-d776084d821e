import '../../domain/entities/appointment.dart';

class AppointmentModel extends Appointment {
  const AppointmentModel({
    super.id,
    required super.doctorId,
    required super.patientName,
    required super.age,
    required super.timeSlotId,
    required super.appointmentType,
    required super.complaintType,
    required super.status,
    required super.reason,
    super.createdAt,
    super.updatedAt,
  });

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    return AppointmentModel(
      id: json['id'] as String?,
      doctorId: json['doctor_id'] as String,
      patientName: json['patient_name'] as String,
      age: json['age'] as int,
      timeSlotId: json['time_slot_id'] as String,
      appointmentType: json['appointment_type'] as String,
      complaintType: json['complaint_type'] as String,
      status: json['status'] as String,
      reason: json['reason'] as String,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'doctor_id': doctorId,
      'patient_name': patientName,
      'age': age,
      'time_slot_id': timeSlotId,
      'appointment_type': appointmentType,
      'complaint_type': complaintType,
      'status': status,
      'reason': reason,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  factory AppointmentModel.fromEntity(Appointment appointment) {
    return AppointmentModel(
      id: appointment.id,
      doctorId: appointment.doctorId,
      patientName: appointment.patientName,
      age: appointment.age,
      timeSlotId: appointment.timeSlotId,
      appointmentType: appointment.appointmentType,
      complaintType: appointment.complaintType,
      status: appointment.status,
      reason: appointment.reason,
      createdAt: appointment.createdAt,
      updatedAt: appointment.updatedAt,
    );
  }

  Appointment toEntity() {
    return Appointment(
      id: id,
      doctorId: doctorId,
      patientName: patientName,
      age: age,
      timeSlotId: timeSlotId,
      appointmentType: appointmentType,
      complaintType: complaintType,
      status: status,
      reason: reason,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
