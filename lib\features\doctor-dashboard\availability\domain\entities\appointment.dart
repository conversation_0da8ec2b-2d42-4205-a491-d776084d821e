import 'package:equatable/equatable.dart';

class Appointment extends Equatable {
  final String? id;
  final String doctorId;
  final String patientName;
  final int age;
  final String timeSlotId;
  final String appointmentType;
  final String complaintType;
  final String status;
  final String reason;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Appointment({
    this.id,
    required this.doctorId,
    required this.patientName,
    required this.age,
    required this.timeSlotId,
    required this.appointmentType,
    required this.complaintType,
    required this.status,
    required this.reason,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        doctorId,
        patientName,
        age,
        timeSlotId,
        appointmentType,
        complaintType,
        status,
        reason,
        createdAt,
        updatedAt,
      ];

  Appointment copyWith({
    String? id,
    String? doctorId,
    String? patientName,
    int? age,
    String? timeSlotId,
    String? appointmentType,
    String? complaintType,
    String? status,
    String? reason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Appointment(
      id: id ?? this.id,
      doctorId: doctorId ?? this.doctorId,
      patientName: patientName ?? this.patientName,
      age: age ?? this.age,
      timeSlotId: timeSlotId ?? this.timeSlotId,
      appointmentType: appointmentType ?? this.appointmentType,
      complaintType: complaintType ?? this.complaintType,
      status: status ?? this.status,
      reason: reason ?? this.reason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
