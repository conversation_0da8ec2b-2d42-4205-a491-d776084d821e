import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_all_address_type/get_all_address_type_data.dart';

class GetAllAddressTypeResponse extends Equatable {
  final GetAllAddressTypeData data;
  final String message;
  final String status;
  final int statusCode;

  const GetAllAddressTypeResponse({
    required this.data,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  @override
  List<Object?> get props => [data, message, status, statusCode];
}
