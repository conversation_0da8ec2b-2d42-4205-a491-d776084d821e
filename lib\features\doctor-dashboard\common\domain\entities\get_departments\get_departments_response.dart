import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_departments/get_departments_data.dart';

class GetDepartmentsResponse extends Equatable {
  final GetDepartmentsData data;
  final int total;
  final int skip;
  final int limit;
  final String message;
  final String status;
  final int statusCode;

  const GetDepartmentsResponse({
    required this.data,
    required this.total,
    required this.skip,
    required this.limit,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  @override
  List<Object?> get props => [
    data,
    total,
    skip,
    limit,
    message,
    status,
    statusCode,
  ];
}
