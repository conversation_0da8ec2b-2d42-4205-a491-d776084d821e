import 'package:equatable/equatable.dart';

class GetAppointmentDetailsByIDData extends Equatable {
  final String appointmentId;
  final String patientName;
  final int age;
  final String gender;
  final String patientPhone;
  final String appointmentType;
  final List<Complaint> complaints;
  final String status;
  final String reason;
  final String date;
  final String startTime;
  final String endTime;
  final List<AppointmentFile> files;

  const GetAppointmentDetailsByIDData({
    required this.appointmentId,
    required this.patientName,
    required this.age,
    required this.gender,
    required this.patientPhone,
    required this.appointmentType,
    required this.complaints,
    required this.status,
    required this.reason,
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.files,
  });

  @override
  List<Object?> get props => [
    appointmentId,
    patientName,
    age,
    patientPhone,
    appointmentType,
    complaints,
    status,
    reason,
    date,
    startTime,
    endTime,
    files,
  ];
}

class Complaint extends Equatable {
  final String label;
  final String value;

  const Complaint({required this.label, required this.value});

  @override
  List<Object?> get props => [label, value];
}

class AppointmentFile extends Equatable {
  final String fileName;

  const AppointmentFile({required this.fileName});

  @override
  List<Object?> get props => [fileName];
}
