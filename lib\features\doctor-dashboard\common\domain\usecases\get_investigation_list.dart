import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_investigation_list/get_investigation_list_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_investigation_list/get_investigation_list_request.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_investigation_list/get_investigation_list_response.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_investigation_list_repositories.dart';

class GetInvestigationList
    implements
        UseCase<GetInvestigationListResponse, GetInvestigationListRequest> {
  final GetInvestigationListRepositories repository;

  GetInvestigationList(this.repository);

  @override
  Future<Either<Failure, GetInvestigationListResponse>> call(
    GetInvestigationListRequest params,
  ) async {
    final requestModel = GetInvestigationListRequestModel.fromEntity(params);
    return await repository.getAllInvestigations(requestModel);
  }
}
