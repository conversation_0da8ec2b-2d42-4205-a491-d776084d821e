class AppointmentDetail {
  final String appointmentId;
  final String patientName;
  final int age;
  final String patientPhone;
  final String appointmentType;
  final List<Complaint> complaints;
  final String status;
  final String reason;
  final String date;
  final String startTime;
  final String endTime;
  final List<AppointmentFile> files;

  AppointmentDetail({
    required this.appointmentId,
    required this.patientName,
    required this.age,
    required this.patientPhone,
    required this.appointmentType,
    required this.complaints,
    required this.status,
    required this.reason,
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.files,
  });
}

class Complaint {
  final String label;
  final String value;

  Complaint({required this.label, required this.value});
}

class AppointmentFile {
  final String fileName;

  AppointmentFile({required this.fileName});
}