import 'dart:developer' as developer;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class TokenStorage {
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _expiresAtKey = 'token_expires_at';

  final FlutterSecureStorage _storage;

  TokenStorage(this._storage);

  Future<void> saveTokens({
    required String accessToken,
    String? refreshToken,
    required DateTime expiresAt,
  }) async {
    try {
      await _storage.write(key: _accessTokenKey, value: accessToken);
      await _storage.write(
        key: _expiresAtKey,
        value: expiresAt.toIso8601String(),
      );

      if (refreshToken != null) {
        await _storage.write(key: _refreshTokenKey, value: refreshToken);
      }

      developer.log('✅ Token saved', name: 'TokenStorage', level: 800);
    } catch (e) {
      developer.log(
        '❌ Error saving token: $e',
        name: 'TokenStorage',
        level: 1000,
      );
      rethrow;
    }
  }

  Future<String?> getAccessToken() async {
    try {
      return await _storage.read(key: _accessTokenKey);
    } catch (e) {
      developer.log(
        '❌ Error reading token: $e',
        name: 'TokenStorage',
        level: 1000,
      );
      return null;
    }
  }

  Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }

  Future<DateTime?> getTokenExpiresAt() async {
    try {
      final expiresAtStr = await _storage.read(key: _expiresAtKey);
      if (expiresAtStr == null) return null;
      return DateTime.parse(expiresAtStr);
    } catch (e) {
      developer.log(
        '❌ Error reading expiry: $e',
        name: 'TokenStorage',
        level: 1000,
      );
      return null;
    }
  }

  Future<bool> isTokenExpired() async {
    final expiresAt = await getTokenExpiresAt();
    if (expiresAt == null) return true;

    // 1 minute buffer
    return DateTime.now().isAfter(
      expiresAt.subtract(const Duration(minutes: 1)),
    );
  }

  Future<void> clearTokens() async {
    try {
      await Future.wait([
        _storage.delete(key: _accessTokenKey),
        _storage.delete(key: _refreshTokenKey),
        _storage.delete(key: _expiresAtKey),
      ]);
      developer.log('✅ Token cleared', name: 'TokenStorage', level: 800);
    } catch (e) {
      developer.log(
        '❌ Error clearing token: $e',
        name: 'TokenStorage',
        level: 1000,
      );
      rethrow;
    }
  }

  Future<String?> getAuthHeader() async {
    final token = await getAccessToken();
    return token != null ? 'Bearer $token' : null;
  }

  Future<void> printTokenInfo() async {
    final token = await getAccessToken();
    final refreshToken = await getRefreshToken();
    final expiresAt = await getTokenExpiresAt();
    final isExpired = await isTokenExpired();

    print('''
╔════════════════════════════════════════════════════════╗
║                    TOKEN INFO                          ║
╠════════════════════════════════════════════════════════╣
║ Access Token:   ${token != null ? '✅ Present' : '❌ Missing'}
║ Refresh Token:  ${refreshToken != null ? '✅ Present' : '❌ Missing'}
║ Expires At:     ${expiresAt?.toString() ?? '❌ N/A'}
║ Is Expired:     ${isExpired ? '⚠️ YES' : '✅ NO'}
╚════════════════════════════════════════════════════════╝
    ''');

    developer.log(
      'Token: ${token != null}, Refresh: ${refreshToken != null}, Expired: $isExpired',
      name: 'TokenStorage',
      level: 800,
    );
  }
}
