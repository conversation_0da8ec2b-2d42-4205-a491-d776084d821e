import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_hospitals/get_hospitals_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_hospitals/get_hospitals_request.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_hospitals/get_hospitals_response.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_hospitals_repositories.dart';

class GetHospitals
    implements UseCase<GetHospitalResponse, GetHospitalsRequest> {
  final GetHospitalsRepositories repository;

  GetHospitals(this.repository);

  @override
  Future<Either<Failure, GetHospitalResponse>> call(
    GetHospitalsRequest params,
  ) async {
    final requestModel = GetHospitalsRequestModel.fromEntity(params);
    return await repository.getAllHospitals(requestModel);
  }
}
