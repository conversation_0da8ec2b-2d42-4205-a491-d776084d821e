import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class ChoiceWidget extends StatefulWidget {
  final bool? isConfirmed;

  final VoidCallback? onConfirm;
  final VoidCallback? onDeny;

  const ChoiceWidget({
    super.key,
    this.isConfirmed,
    this.onConfirm,
    this.onDeny,
  });

  @override
  State<ChoiceWidget> createState() => _ChoiceWidgetState();
}

class _ChoiceWidgetState extends State<ChoiceWidget> {
  bool? _isConfirmed;

  @override
  void initState() {
    super.initState();
    _isConfirmed = widget.isConfirmed;
  }

  @override
  Widget build(BuildContext context) {
    return (_isConfirmed == null)
        ? Row(
          children: [
            GestureDetector(
              onTap: () {
                setState(() {
                  _isConfirmed = false;
                });
                if (widget.onDeny != null) {
                  widget.onDeny!();
                }
              },
              child: ReusableSvgImage(
                assetPath: "assets/icons/doctor/cross-icon.svg",
                width: 24,
                height: 24,
              ),
            ),
            SizedBox(width: AppConstants.sizedBoxHeightSmall),
            GestureDetector(
              onTap: () {
                setState(() {
                  _isConfirmed = true;
                });
                if (widget.onConfirm != null) {
                  widget.onConfirm!();
                }
              },
              child: ReusableSvgImage(
                assetPath: "assets/icons/doctor/tick-icon.svg",
                width: 24,
                height: 24,
              ),
            ),
          ],
        )
        : Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _isConfirmed! ? Color(0xFFE9F6FE) : Color(0xFFFEEEEF),
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          ),
          child: Text(
            _isConfirmed! ? 'Confirmed' : 'Declined',
            style: TextStyle(
              color: _isConfirmed! ? Color(0xFF7A75EB) : Color(0xFFF1553D),
              fontWeight: FontWeight.w400,
              fontSize: AppConstants.fontSize14,
            ),
          ),
        );
  }
}
