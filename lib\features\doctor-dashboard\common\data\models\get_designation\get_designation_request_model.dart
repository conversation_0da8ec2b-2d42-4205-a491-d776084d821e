import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_designation/get_designation_request.dart';

class GetDesignationsRequestModel extends GetDesignationRequest {
  const GetDesignationsRequestModel({
    required super.limit,
    required super.skip,
  });

  factory GetDesignationsRequestModel.fromEntity(GetDesignationRequest entity) {
    return GetDesignationsRequestModel(limit: entity.limit, skip: entity.skip);
  }

  Map<String, dynamic> toJson() {
    return {'limit': limit, 'skip': skip};
  }
}
