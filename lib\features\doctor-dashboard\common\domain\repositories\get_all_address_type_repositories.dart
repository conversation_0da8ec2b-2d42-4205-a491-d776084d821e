import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_all_address_type/get_all_address_type_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_all_address_type/get_all_address_type_response_model.dart';

abstract class GetAllAddressTypeRepositories {
  Future<Either<Failure, GetAllAddressTypeResponseModel>> getAllAddressType(
    GetAllAddressTypeRequestModel requestModel,
  );
}
