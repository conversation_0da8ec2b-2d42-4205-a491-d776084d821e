import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_designation/get_designation_data.dart';

class GetDesignationResponse extends Equatable {
  final GetDesignationData data;
  final String message;
  final String status;
  final int statusCode;

  const GetDesignationResponse({
    required this.data,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  @override
  List<Object?> get props => [data, message, status, statusCode];
}
