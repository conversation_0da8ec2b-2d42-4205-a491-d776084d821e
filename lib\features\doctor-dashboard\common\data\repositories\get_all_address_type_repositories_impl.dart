import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_all_address_type_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_all_address_type/get_all_address_type_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_all_address_type/get_all_address_type_response_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_all_address_type_repositories.dart';

class GetAllAddressTypeRepositoriesImpl
    implements GetAllAddressTypeRepositories {
  final GetAllAddressTypeRemoteDatasource remoteDatasource;

  GetAllAddressTypeRepositoriesImpl({required this.remoteDatasource});

  @override
  Future<Either<Failure, GetAllAddressTypeResponseModel>> getAllAddressType(
    GetAllAddressTypeRequestModel requestModel,
  ) async {
    try {
      final response = await remoteDatasource.getAllAddressType(requestModel);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
