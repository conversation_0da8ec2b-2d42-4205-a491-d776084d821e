import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import '../entities/appointment.dart';
import '../entities/doctor_availability.dart';
import '../entities/doctor_availability_schedule.dart';
import '../entities/time_slot.dart';

abstract class AvailabilityRepository {
  /// Create a new appointment
  Future<Either<Failure, Appointment>> createAppointment(
    Appointment appointment,
  );

  /// Get doctor availability for a specific date
  Future<Either<Failure, DoctorAvailability>> getDoctorAvailability({
    required String doctorId,
    required DateTime date,
  });

  /// Get available time slots for a doctor on a specific date
  Future<Either<Failure, List<TimeSlot>>> getAvailableTimeSlots({
    required String doctorId,
    required DateTime date,
  });

  /// Get all appointments for a patient
  Future<Either<Failure, List<Appointment>>> getPatientAppointments({
    required String patientId,
  });

  /// Get all appointments for a doctor
  Future<Either<Failure, List<Appointment>>> getDoctorAppointments({
    required String doctorId,
    DateTime? date,
  });

  /// Update appointment status
  Future<Either<Failure, Appointment>> updateAppointmentStatus({
    required String appointmentId,
    required String status,
  });

  /// Cancel appointment
  Future<Either<Failure, bool>> cancelAppointment({
    required String appointmentId,
  });

  /// Set doctor availability schedule
  Future<Either<Failure, DoctorAvailabilitySchedule>>
  setDoctorAvailabilitySchedule(DoctorAvailabilitySchedule schedule);
}
