import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_response.dart';

abstract class UpdateAppointmentStatusByAppointmentidRemoteDatasource {
  Future<UpdateAppointmentStatusByAppointmentidResponseModel>
  updateAppointmentStatusByAppointmentid(
    UpdateAppointmentStatusByAppointmentidRequestModel request,
  );
}

class UpdateAppointmentStatusByAppointmentidRemoteDatasourceImpl
    implements UpdateAppointmentStatusByAppointmentidRemoteDatasource {
  final DioClient dioClient;

  UpdateAppointmentStatusByAppointmentidRemoteDatasourceImpl({
    required this.dioClient,
  });

  @override
  Future<UpdateAppointmentStatusByAppointmentidResponseModel>
  updateAppointmentStatusByAppointmentid(
    UpdateAppointmentStatusByAppointmentidRequestModel request,
  ) async {
    try {
      // final queryParams = request.toJson();

      // final response = await dioClient.update(
      //   '/doctors/update-appointment-status/${request.appointmentId}',
      // );
      // return GetAppointmentsByDoctorResponseModel.fromJson(response.data);

      // await Future.delayed(const Duration(seconds: 2));

      final responseJson = {
        "data": {"id": "f4360c86-6996-42ca-bb0b-42604f5bff27"},
        "message": "Appointment status updated successfully",
        "status": "success",
        "statusCode": 200,
      };

      return UpdateAppointmentStatusByAppointmentidResponseModel.fromJson(
        responseJson,
      );
    } catch (e) {
      throw ServerException();
    }
  }
}
