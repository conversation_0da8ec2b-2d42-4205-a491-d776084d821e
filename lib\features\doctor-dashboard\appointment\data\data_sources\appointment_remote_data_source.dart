import '../models/appointment_detail_model.dart';

abstract class AppointmentDetailRemoteDataSource {
  Future<AppointmentDetailModel> getAppointmentDetail(String appointmentId);
}

class AppointmentDetailRemoteDataSourceImpl
    implements AppointmentDetailRemoteDataSource {
  @override
  Future<AppointmentDetailModel> getAppointmentDetail(
      String appointmentId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Dummy data matching your structure
    final dummyData = {
      "data": {
        "appointment_id": "fc07cec1-6af4-46b3-84c3-c95586c1ef6d",
        "patient_name": "<PERSON>",
        "age": 43,
        "patient_phone": "555-020212",
        "appointment_type": "virtual",
        "complaints": [
          {
            "label": "Back pain",
            "value": "complaint-uuid"
          },
          {
            "label": "Headache",
            "value": "complaint-uuid-2"
          },
          {
            "label": "Fatigue",
            "value": "complaint-uuid-3"
          }
        ],
        "status": "requested",
        "reason":
        "Recurring lower back pain for 2 weeks. Patient reports difficulty sitting for long periods and sharp pain when bending.",
        "date": "2025-08-12",
        "start_time": "10:30:00",
        "end_time": "11:00:00",
        "files": [
          {
            "file_name": "medical_report.pdf"
          },
          {
            "file_name": "xray_results.pdf"
          },
          {
            "file_name": "lab_tests.pdf"
          }
        ]
      },
      "message": "Appointments details fetched successfully",
      "status": "success",
      "statusCode": 200
    };

    return AppointmentDetailModel.fromJson(dummyData);
  }
}