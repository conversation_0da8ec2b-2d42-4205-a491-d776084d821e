import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import '../bloc/availability_event.dart' show TimeSlotData;

class TimeSlotRow extends StatelessWidget {
  final TimeSlotData slot;
  final Function(TimeSlotData, bool) onTimeSelect;
  final Function(TimeSlotData) onDelete;

  const TimeSlotRow({
    super.key,
    required this.slot,
    required this.onTimeSelect,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(
            'Start Time',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textHighColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: InkWell(
              onTap: () => onTimeSelect(slot, true),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  color: AppConstants.inputFieldBackgroundColor,
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadiusSmall,
                  ),
                  border: Border.all(
                    color: AppConstants.inputFieldForegroundColor,
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      slot.startTime,
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        color: AppConstants.textPrimaryColor,
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      Icons.access_time,
                      color: AppConstants.primaryColor,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'End Time',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textHighColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextField(
              readOnly: true,
              onTap: () => onTimeSelect(slot, false),
              controller: TextEditingController(text: slot.endTime),
              decoration: InputDecoration(
                hintText: 'Select end time',
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                filled: true,
                fillColor: AppConstants.inputFieldBackgroundColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadiusSmall,
                  ),
                  borderSide: BorderSide(
                    color: AppConstants.inputFieldForegroundColor,
                  ),
                ),
                suffixIcon: Icon(
                  Icons.access_time,
                  color: AppConstants.primaryColor,
                  size: 16,
                ),
              ),
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textPrimaryColor,
              ),
            ),
          ),
          const SizedBox(width: 12),
          IconButton(
            onPressed: () => onDelete(slot),
            icon: Icon(
              Icons.delete_outline,
              color: AppConstants.errorColor,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }
}
