import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/di/injection_container.dart' as di;
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/presentation/bloc/availability_bloc.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/presentation/pages/availability_page.dart';

final availabilityRoute = [
  GoRoute(
    path: AppRouter.availability,
    builder: (BuildContext context, GoRouterState state) {
      return BlocProvider(
        create: (context) => di.sl<AvailabilityBloc>(),
        child: const AvailabilityPage(),
      );
    },
  ),
];
