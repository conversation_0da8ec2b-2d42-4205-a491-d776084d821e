import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_all_address_type_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_departments_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_designation_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_hospitals_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_investigation_list_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_all_address_type/get_all_address_type_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_departments/get_departments_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_designation/get_designation_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_hospitals/get_hospitals_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_investigation_list/get_investigation_list_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/bloc/dropdown_items/dropdown_items_event.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/bloc/dropdown_items/dropdown_items_state.dart';

class DropdownItemsBloc extends Bloc<DropdownItemsEvent, DropDownItemsState> {
  final GetAllAddressTypeRemoteDatasource getAllAddressTypeRemoteDatasource;
  final GetDepartmentsRemoteDatasource getDepartmentsRemoteDatasource;
  final GetDesignationsRemoteDatasource getDesignationsRemoteDatasource;
  final GetHospitalsRemoteDatasource getHospitalsRemoteDatasource;
  final GetInvestigationListRemoteDatasource
  getInvestigationListRemoteDatasource;

  DropdownItemsBloc({
    required this.getAllAddressTypeRemoteDatasource,
    required this.getDepartmentsRemoteDatasource,
    required this.getDesignationsRemoteDatasource,
    required this.getHospitalsRemoteDatasource,
    required this.getInvestigationListRemoteDatasource,
  }) : super(DropDownItemsInitial()) {
    on<CallGetAllDropDownItems>((event, emit) async {
      if (state is DropDownItemsLoaded) return;
      emit(DropDownItemsLoading());
      try {
        final addressTypeResponse = await getAllAddressTypeRemoteDatasource
            .getAllAddressType(GetAllAddressTypeRequestModel());

        final departmentResponse = await getDepartmentsRemoteDatasource
            .getAllDepartments(
              GetDepartmentsRequestModel(limit: 1000, skip: 0),
            );

        final designationResponse = await getDesignationsRemoteDatasource
            .getAllDesignations(
              GetDesignationsRequestModel(limit: 1000, skip: 0),
            );
        final hospitalsResponse = await getHospitalsRemoteDatasource
            .getHospitals(GetHospitalsRequestModel(limit: 1000, skip: 0));

        final investigationListResponse =
            await getInvestigationListRemoteDatasource.getAllInvestigations(
              GetInvestigationListRequestModel(limit: 1000, skip: 0),
            );

        emit(
          DropDownItemsLoaded(
            addressData: addressTypeResponse.data,
            departmentData: departmentResponse.data,
            designationData: designationResponse.data,
            hospitalsData: hospitalsResponse.data,
            investigationListData: investigationListResponse.data,
          ),
        );
      } catch (e) {
        emit(DropDownItemsError(message: e.toString()));
      }
    });
  }
}
