import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_by_doctor/get_appointments_by_doctor_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_details_by_id/get_appointments_details_by_id_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointment_details_by_id/get_appointment_details_by_id_data.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointment_details_by_id/get_appointment_details_by_id_request.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointment_details_by_id_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointments_by_doctor_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/update_appointment_status_by_appointmentid_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/dashboard/dashboard_event.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/dashboard/dashboard_state.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final GetAppointmentByDoctorRepositories getAppointmentbyDoctorRepositories;
  final GetAppointmentDetailsByIDRepositories
  getAppointmentDetailsByIDRepositories;
  final UpdateAppointmentStatusByAppointmentidRepositories
  updateAppointmentStatusByAppointmentidRepositories;

  DashboardBloc({
    required this.getAppointmentbyDoctorRepositories,
    required this.getAppointmentDetailsByIDRepositories,
    required this.updateAppointmentStatusByAppointmentidRepositories,
  }) : super(DashboardInitial()) {
    on<CallGetDashboardData>(_onGetDashboardData);
    on<CallUpdateAppointmentStatusByAppointmentId>(
      _onUpdateAppointmentStatusByAppointmentId,
    );
  }

  Future<void> _onGetDashboardData(
    CallGetDashboardData event,
    Emitter<DashboardState> emit,
  ) async {
    emit(DashboardLoading());

    final appointmentRequestsResult = await _getAppointments(
      requestModel: GetAppointmentsByDoctorRequestModel(
        startDate: DateTime.now().toString(),
        endDate: DateTime.now().add(const Duration(days: 7)).toString(),
        skip: 0,
        limit: 50,
      ),
    );

    // Get Today's Appointments
    final todayAppointmentsResult = await _getAppointments(
      requestModel: GetAppointmentsByDoctorRequestModel(
        startDate: DateTime.now().toIso8601String().split('T')[0],
        endDate: DateTime.now().toIso8601String().split('T')[0],
        skip: 0,
        limit: 50, // 50 is max
      ),
    );

    // Get Recent Patients
    final recentPatientsResult = await _getAppointments(
      requestModel: GetAppointmentsByDoctorRequestModel(
        month: DateTime.now().month,
        year: DateTime.now().year,
        skip: 0,
        limit: 10,
        status: 'completed',
      ),
    );

    if (!emit.isDone) {
      emit(
        DashboardLoaded(
          todayAppointments: TodayAppointmentsState(
            appointments: todayAppointmentsResult,
            isLoading: false,
          ),
          appointmentRequests: AppointmentRequestsState(
            appointments: appointmentRequestsResult,
            isLoading: false,
          ),
          recentPatients: RecentPatientsState(
            appointments: recentPatientsResult,
            isLoading: false,
          ),
          patientRecord: PatientRecordState(
            currentPatientCount: 120,
            previousPatientCount: 100,
            previousPatientRatio: (100 - 100) / 100,
            currentPatientRatio: (120 - 100) / 100,
            isLoading: false,
          ),
        ),
      );
    }
  }

  Future<List<GetAppointmentDetailsByIDData>> _getAppointments({
    required GetAppointmentsByDoctorRequestModel requestModel,
  }) async {
    final result = await getAppointmentbyDoctorRepositories
        .getAppointmentsByDoctor(requestModel);

    return await result.fold((failure) async => [], (response) async {
      List<GetAppointmentDetailsByIDData> data = [];
      for (var appointment in response.data) {
        if (appointment.appointmentID?.isEmpty ?? true) {
          data.add(
            GetAppointmentDetailsByIDData(
              appointmentId: appointment.bookingID,
              patientName: "Patient ${appointment.bookingID}",
              age: 0,
              gender: "unknown",
              patientPhone: appointment.phone,
              appointmentType: 'Regular',
              complaints: List<Complaint>.empty(),
              status: appointment.status,
              reason: appointment.disease,
              date: DateTime.now().toString().split(' ')[0],
              startTime: appointment.time,
              endTime: appointment.time,
              files: const [],
            ),
          );
          continue;
        }

        final appointmentDetails = GetAppointmentDetailsByIDRequest(
          appointmentId: appointment.appointmentID ?? '',
        );

        final detailsResult = await getAppointmentDetailsByIDRepositories
            .getAppointmentDetailsById(
              GetAppointmentsDetailsByIDRequestModel.fromEntity(
                appointmentDetails,
              ),
            );

        data.add(
          detailsResult.fold(
            (failure2) => GetAppointmentDetailsByIDData(
              appointmentId: appointment.bookingID,
              patientName: "Patient ${appointment.bookingID}",
              age: 0,
              gender: "unknown",
              patientPhone: appointment.phone,
              appointmentType: 'Regular',
              complaints: List<Complaint>.empty(),
              status: appointment.status,
              reason: appointment.disease,
              date: DateTime.now().toString().split(' ')[0],
              startTime: appointment.time,
              endTime: appointment.time,
              files: const [],
            ),
            (response2) => GetAppointmentDetailsByIDData(
              appointmentId: response2.data.appointmentId,
              patientName: response2.data.patientName,
              age: response2.data.age,
              gender: response2.data.gender,
              patientPhone: response2.data.patientPhone,
              appointmentType: response2.data.appointmentType,
              complaints: response2.data.complaints,
              status: response2.data.status,
              reason: response2.data.reason,
              date: response2.data.date,
              startTime: response2.data.startTime,
              endTime: response2.data.endTime,
              files: response2.data.files,
            ),
          ),
        );
      }
      return data;
    });
  }

  Future<void> _onUpdateAppointmentStatusByAppointmentId(
    CallUpdateAppointmentStatusByAppointmentId event,
    Emitter<DashboardState> emit,
  ) async {
    // Return early if not in loaded state
    if (state is! DashboardLoaded) {
      emit(DashboardError(message: 'Invalid state: Dashboard not loaded'));
      return;
    }

    final currentState = state as DashboardLoaded;

    // Set the updating appointment ID to show loading state
    emit(
      currentState.copyWith(
        updatingAppointmentId:
            event.appointmentStatusByAppointmentidRequest.appointmentId,
      ),
    );

    try {
      final updateAppointmentStatusRequestModel =
          UpdateAppointmentStatusByAppointmentidRequestModel.fromEntity(
            event.appointmentStatusByAppointmentidRequest,
          );

      // Call the repository to update the appointment status
      final result = await updateAppointmentStatusByAppointmentidRepositories
          .updateAppointmentStatusByAppointmentid(
            updateAppointmentStatusRequestModel,
          );

      // Handle the result
      result.fold(
        (failure) => emit(
          DashboardError(message: 'Failed to update appointment status'),
        ),
        (response) {
          // Clear the updating state and refresh data
          if (state is DashboardLoaded) {
            final loadedState = state as DashboardLoaded;
            emit(loadedState.copyWith(updatingAppointmentId: null));
            // Refresh dashboard data
            add(CallGetDashboardData());
          }
        },
      );
    } catch (e) {
      // Handle unexpected errors
      emit(DashboardError(message: e.toString()));
    } finally {
      // Ensure loading state is cleared if we're still in the same state
      if (state is DashboardLoaded) {
        final loadedState = state as DashboardLoaded;
        if (loadedState.updatingAppointmentId ==
            event.appointmentStatusByAppointmentidRequest.appointmentId) {
          emit(loadedState.copyWith(updatingAppointmentId: null));
        }
      }
    }
  }
}
