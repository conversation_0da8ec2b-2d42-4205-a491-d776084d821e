import 'package:equatable/equatable.dart';

abstract class Doctor<PERSON><PERSON><PERSON>State extends Equatable {
  @override
  List<Object?> get props => [];
}

class DoctorH<PERSON>lightsInitial extends DoctorH<PERSON><PERSON>State {}

class DoctorHighlightsLoading extends Doctor<PERSON><PERSON><PERSON>State {}

class Doctor<PERSON><PERSON>lightsLoaded extends Doctor<PERSON><PERSON><PERSON>State {
  final int? totalPatients;
  final int? totalAppointments;
  final int? totalClinicConsulting;
  final int? totalVideoConsulting;

  DoctorHighlightsLoaded({
    this.totalPatients = 0,
    this.totalAppointments = 0,
    this.totalClinicConsulting = 0,
    this.totalVideoConsulting = 0,
  });
}

class Doctor<PERSON>ighlightsError extends Doctor<PERSON><PERSON>lightsState {
  final String message;
  final String? status;

  DoctorHighlightsError({required this.message, this.status});
  @override
  List<Object?> get props => [message];
}
