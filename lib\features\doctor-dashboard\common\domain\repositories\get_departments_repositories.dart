import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_departments/get_departments_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_departments/get_departments_response_model.dart';

abstract class GetDepartmentsRepositories {
  Future<Either<Failure, GetDepartmentsResponseModel>> getAllDepartments(
    GetDepartmentsRequestModel requestModel,
  );
}
