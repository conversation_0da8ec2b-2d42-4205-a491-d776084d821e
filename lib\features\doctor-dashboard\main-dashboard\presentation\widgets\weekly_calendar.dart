import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class WeeklyCalendar extends StatefulWidget {
  const WeeklyCalendar({super.key});

  @override
  State<WeeklyCalendar> createState() => _WeeklyCalendarState();
}

class _WeeklyCalendarState extends State<WeeklyCalendar> {
  DateTime currentCalendarDateFocused = DateTime.now();
  int calendarLowerBoundDate = 0;
  int calendarUpperBoundDate = 0;
  late String calendarDateRange;
  String _monthName(int month) {
    const monthNames = [
      '',
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return monthNames[month];
  }

  void currentWeekRange() {
    DateTime focusedDate = currentCalendarDateFocused;
    // In Dart, weekday: 1=Mon, ..., 7=Sun. To get Sunday as start:
    DateTime startOfWeek = focusedDate.subtract(
      Duration(days: focusedDate.weekday % 7),
    );
    DateTime endOfWeek = startOfWeek.add(const Duration(days: 6));

    // Leap year check for February
    bool isLeapYear(int year) {
      return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    int daysInMonth(int year, int month) {
      if (month == 2) {
        return isLeapYear(year) ? 29 : 28;
      }
      const monthLengths = [
        31, // Jan
        28, // Feb (handled above)
        31, // Mar
        30, // Apr
        31, // May
        30, // Jun
        31, // Jul
        31, // Aug
        30, // Sep
        31, // Oct
        30, // Nov
        31, // Dec
      ];
      return monthLengths[month - 1];
    }

    int endDay = endOfWeek.day;
    int endMonth = endOfWeek.month;
    int endYear = endOfWeek.year;

    int maxEndDay = daysInMonth(endYear, endMonth);
    if (endDay > maxEndDay) {
      endDay = maxEndDay;
      endOfWeek = DateTime(endYear, endMonth, endDay);
    }

    String formattedStart = "${startOfWeek.day}";
    String formattedEnd =
        "${endOfWeek.day} ${_monthName(endOfWeek.month)}, ${endOfWeek.year}";
    calendarDateRange = "$formattedStart-$formattedEnd";
    calendarLowerBoundDate = startOfWeek.day;
    calendarUpperBoundDate = endOfWeek.day;
  }

  @override
  void initState() {
    super.initState();
    currentWeekRange();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        children: [
          Row(
            children: [
              SizedBox(
                width: 200,
                child: Text(
                  calendarDateRange,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppConstants.textHighColor,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              SizedBox(width: 16),
              GestureDetector(
                onTap: () {
                  setState(() {
                    DateTime focusedDate = currentCalendarDateFocused;
                    DateTime prevWeek = focusedDate.subtract(
                      const Duration(days: 7),
                    );
                    currentCalendarDateFocused = prevWeek;
                    currentWeekRange();
                  });
                },
                child: ReusableSvgImage(
                  assetPath: "assets/icons/doctor/box-arrow-left-icon.svg",
                  width: 20,
                  height: 20,
                ),
              ),
              SizedBox(width: 8),
              GestureDetector(
                onTap: () {
                  setState(() {
                    DateTime focusedDate = currentCalendarDateFocused;
                    DateTime nextWeek = focusedDate.add(
                      const Duration(days: 7),
                    );
                    currentCalendarDateFocused = nextWeek;
                    currentWeekRange();
                  });
                },
                child: ReusableSvgImage(
                  assetPath: "assets/icons/doctor/box-arrow-right-icon.svg",
                  width: 20,
                  height: 20,
                ),
              ),
            ],
          ),
          SizedBox(height: 7),
          Table(
            columnWidths: const {
              0: FlexColumnWidth(),
              1: FlexColumnWidth(),
              2: FlexColumnWidth(),
              3: FlexColumnWidth(),
              4: FlexColumnWidth(),
              5: FlexColumnWidth(),
              6: FlexColumnWidth(),
            },
            children: [
              TableRow(
                children: [
                  for (var day in ['S', 'M', 'T', 'W', 'T', 'F', 'S'])
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Center(
                        child: Text(
                          day,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppConstants.textHighColor,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              TableRow(
                children: [
                  for (int i = 0; i < 7; i++)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Center(
                        child: Text(
                          currentCalendarDateFocused
                              .subtract(
                                Duration(
                                  days: currentCalendarDateFocused.weekday,
                                ),
                              )
                              .add(Duration(days: i))
                              .day
                              .toString(),
                          style: TextStyle(
                            color:
                                (() {
                                  DateTime startOfWeek =
                                      currentCalendarDateFocused.subtract(
                                        Duration(
                                          days:
                                              currentCalendarDateFocused
                                                  .weekday,
                                        ),
                                      );
                                  DateTime date = startOfWeek.add(
                                    Duration(days: i),
                                  );
                                  DateTime today = DateTime.now();
                                  return date.year == today.year &&
                                          date.month == today.month &&
                                          date.day == today.day
                                      ? AppConstants.primaryColor
                                      : Colors.black;
                                })(),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
