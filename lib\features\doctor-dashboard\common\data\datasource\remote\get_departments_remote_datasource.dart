import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_departments/get_departments_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_departments/get_departments_response_model.dart';

abstract class GetDepartmentsRemoteDatasource {
  Future<GetDepartmentsResponseModel> getAllDepartments(
    GetDepartmentsRequestModel requestModel,
  );
}

class GetDepartmentsRemoteDatasourceImpl
    implements GetDepartmentsRemoteDatasource {
  final DioClient dioClient;

  GetDepartmentsRemoteDatasourceImpl({required this.dioClient});

  @override
  Future<GetDepartmentsResponseModel> getAllDepartments(
    GetDepartmentsRequestModel request,
  ) async {
    try {
      // final queryParams = request.toJson();

      // final response = await dioClient.get(
      //   '{{{{devURL}}/doctors/departments?skip=${request.skip}&limit=${request.limit}',
      // );
      // return GetDepartmentsResponseModel.fromJson(response);

      await Future.delayed(const Duration(seconds: 2));

      final responseJson = {
        "data": [
          {
            "label": "Medicine",
            "value": "c3a9c6a7-f63e-43b4-9114-5b98080e7bac",
          },
        ],
        "total": 1,
        "skip": 0,
        "limit": 5,
        "message": "Doctor departments fetched successfully",
        "status": "success",
        "statusCode": 200,
      };

      return GetDepartmentsResponseModel.fromJson(responseJson);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
