import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/analytics/get_doctor_appointment_statistics_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/doctor_highlights/doctor_highlights_event.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/doctor_highlights/doctor_highlights_state.dart';

class DoctorHighlightsBloc
    extends Bloc<DoctorHighlightsEvent, DoctorHighlightsState> {
  final GetDoctorAppointmentStatisticsRepositories
  getDoctorAppointmentStatisticsRepositories;

  DoctorHighlightsBloc({
    required this.getDoctorAppointmentStatisticsRepositories,
  }) : super(DoctorHighlightsInitial()) {
    on<CallGetDoctorHighlightsData>(_onGetDoctorHighlightsData);
  }

  Future<void> _onGetDoctorHighlightsData(
    CallGetDoctorHighlightsData event,
    Emitter<DoctorHighlightsState> emit,
  ) async {
    emit(DoctorHighlightsLoading());

    final result = await getDoctorAppointmentStatisticsRepositories
        .getDoctorAppointmentStatistics(
          GetDoctorAppointmentStatisticsRequestModel(),
        );

    result.fold(
      (failure) => emit(
        DoctorHighlightsError(
          message: 'Failed to fetch appointment statistics',
        ),
      ),
      (response) {
        emit(
          DoctorHighlightsLoaded(
            totalPatients: response.data.totalPatients,
            totalAppointments: response.data.totalAppointments,
            totalClinicConsulting: response.data.totalClinicConsulting,
            totalVideoConsulting: response.data.totalVirtualConsulting,
          ),
        );
      },
    );
  }
}
