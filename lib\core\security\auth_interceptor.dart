import 'dart:developer' as developer;
import 'package:dio/dio.dart';
import 'package:imed_fe/core/security/services/token_service.dart';

class AuthInterceptor extends Interceptor {
  final TokenService _tokenService;

  AuthInterceptor(this._tokenService);

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    developer.log(
      '→ ${options.method} ${options.path}',
      name: 'AuthInterceptor',
      level: 800,
    );

    final isValid = await _tokenService.validateAndRefreshToken();
    if (!isValid) {
      developer.log(
        '❌ Token validation failed',
        name: 'AuthInterceptor',
        level: 1000,
      );
      handler.reject(
        DioException(
          requestOptions: options,
          error: 'Token expired - please login again',
          type: DioExceptionType.unknown,
        ),
      );
      return;
    }

    final token = await _tokenService.getAccessToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
      developer.log(
        '✅ Token added to header',
        name: 'AuthInterceptor',
        level: 800,
      );
    }

    handler.next(options);
  }

  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    developer.log(
      '← Error ${err.response?.statusCode}',
      name: 'AuthInterceptor',
      level: 1000,
    );

    if (err.response?.statusCode == 401) {
      developer.log(
        '⚠️ 401 - Attempting token refresh...',
        name: 'AuthInterceptor',
        level: 900,
      );

      final success = await _tokenService.validateAndRefreshToken();
      if (success) {
        final token = await _tokenService.getAccessToken();
        if (token != null) {
          err.requestOptions.headers['Authorization'] = 'Bearer $token';
          developer.log(
            '🔄 Retrying request...',
            name: 'AuthInterceptor',
            level: 800,
          );

          try {
            final response = await Dio().request(
              err.requestOptions.path,
              options: Options(
                method: err.requestOptions.method,
                headers: err.requestOptions.headers,
              ),
              data: err.requestOptions.data,
              queryParameters: err.requestOptions.queryParameters,
            );
            handler.resolve(response);
            return;
          } catch (e) {
            developer.log(
              '❌ Retry failed: $e',
              name: 'AuthInterceptor',
              level: 1000,
            );
          }
        }
      }

      await _tokenService.logout();
    }

    handler.next(err);
  }

  @override
  Future<void> onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) async {
    developer.log(
      '← Response ${response.statusCode}',
      name: 'AuthInterceptor',
      level: 800,
    );
    handler.next(response);
  }
}
