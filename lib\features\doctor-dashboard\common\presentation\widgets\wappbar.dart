import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/widgets/feedback_box.dart';

class WAppBar extends StatelessWidget {
  const WAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                const SizedBox(width: 24),
                Spacer(),
                // Expanded(
                //   child: Align(
                //     alignment: Alignment.center,
                //     child: ConstrainedBox(
                //       constraints: const BoxConstraints(maxWidth: 379),
                //       child: CustomSearchBar(hintText: "Search"),
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
          const SizedBox(width: 24),
          const AppBarOptions(),
        ],
      ),
    );
  }
}

class AppBarOptions extends StatelessWidget {
  const AppBarOptions({super.key});

  void _showFeedback(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) =>
              Dialog(alignment: Alignment(1.0, -0.6), child: FeedbackBox()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            ReusableSvgImage(
              assetPath: "assets/icons/doctor/translate-icon.svg",
              width: 20,
              height: 20,
            ),
            const SizedBox(width: 7),
            DropdownButton<String>(
              value: 'English (US)',
              items: const [
                DropdownMenuItem(
                  value: 'English (US)',
                  child: Text('English (US)'),
                ),
                DropdownMenuItem(
                  value: 'Spanish (ES)',
                  child: Text('Spanish (ES)'),
                ),
                DropdownMenuItem(
                  value: 'French (FR)',
                  child: Text('French (FR)'),
                ),
              ],
              onChanged: (value) {},
              underline: SizedBox(),
              style: const TextStyle(
                color: AppConstants.textMidColor,
                fontSize: 12,
              ),
              icon: ReusableSvgImage(
                assetPath: "assets/icons/doctor/dropdown.svg",
                width: 12,
                height: 12,
                color: AppConstants.textMidColor,
              ),
            ),
          ],
        ),
        const SizedBox(width: 10),
        IconButton(
          icon: ReusableSvgImage(
            assetPath: "assets/icons/doctor/notification-with-number.svg",
            width: 30,
          ),
          onPressed: () {},
        ),
        IconButton(
          icon: ReusableSvgImage(
            assetPath: "assets/icons/doctor/message-with-number.svg",
            width: 30,
          ),
          onPressed: () {},
        ),
        IconButton(
          icon: ReusableSvgImage(
            assetPath: "assets/icons/doctor/feedback-icon.svg",
            width: 30,
          ),
          onPressed: () {
            _showFeedback(context);
          },
        ),
        const SizedBox(width: 16),
        CircleAvatar(
          radius: 14.5,
          backgroundImage:
              Image(image: AssetImage("assets/images/person4.png")).image,
        ),
        const SizedBox(width: 10),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Dr. Kiran',
              style: TextStyle(fontSize: 14, color: Colors.black),
            ),
            const Text(
              'Dentist',
              style: TextStyle(fontSize: 10, color: AppConstants.textMidColor),
            ),
          ],
        ),
        const SizedBox(width: 16),
        IconButton(
          onPressed: () {},
          icon: ReusableSvgImage(
            assetPath: "assets/icons/doctor/more_vert.svg",
            width: 32,
            height: 32,
          ),
        ),
      ],
    );
  }
}
