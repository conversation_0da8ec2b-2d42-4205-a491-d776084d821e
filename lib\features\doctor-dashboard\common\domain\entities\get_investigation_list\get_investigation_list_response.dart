import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_investigation_list/get_investigation_list_data.dart';

class GetInvestigationListResponse extends Equatable {
  final GetInvestigationListData data;
  final int total;
  final String skip;
  final String limit;
  final String? message;
  final String? status;
  final int? statusCode;

  const GetInvestigationListResponse({
    required this.data,
    required this.skip,
    required this.limit,
    required this.total,
    this.message,
    this.status,
    this.statusCode,
  });

  @override
  List<Object?> get props => [
    data,
    skip,
    limit,
    total,
    message,
    status,
    statusCode,
  ];
}
