import 'package:get_it/get_it.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/analytics/get_doctor_appointment_statistics_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/appointment/get_appointment_details_by_id_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/appointment/get_appointments_by_doctor_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/appointment/update_appointment_status_by_appointmentid_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/repositories/analytics/get_doctor_appointment_statistics_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/analytics/get_doctor_appointment_statistics_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/usecases/analytics/get_doctor_appointment_statistics.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/repositories/appointments/get_appointment_details_by_id_repositries_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/repositories/appointments/get_appointments_by_doctor_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/repositories/appointments/update_appointment_status_by_appointmentid_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointment_details_by_id_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointments_by_doctor_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/update_appointment_status_by_appointmentid_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/usecases/appointment/get_appointment_details_by_id.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/usecases/appointment/get_appointments_by_doctor.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/usecases/appointment/update_appointment_status_by_appointmentid.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/dashboard/dashboard_bloc.dart';

final sl = GetIt.instance;

void injectDashboard() {
  // Blocs
  sl.registerFactory(
    () => DashboardBloc(
      getAppointmentbyDoctorRepositories: sl(),
      getAppointmentDetailsByIDRepositories: sl(),
      updateAppointmentStatusByAppointmentidRepositories: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetAppointmentsByDoctor(sl()));
  sl.registerLazySingleton(() => GetAppointmentDetailsById(sl()));
  sl.registerLazySingleton(() => UpdateAppointmentStatusByAppointmentid(sl()));
  sl.registerLazySingleton(() => GetDoctorAppointmentStatistics(sl()));

  // Repositories
  sl.registerLazySingleton<GetAppointmentByDoctorRepositories>(
    () => GetAppointmentByDoctorRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<GetAppointmentDetailsByIDRepositories>(
    () => GetAppointmentDetailsByIDRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<UpdateAppointmentStatusByAppointmentidRepositories>(
    () => UpdateAppointmentStatusByAppointmentidRepositoriesImpl(
      remoteDatasource: sl(),
    ),
  );
  sl.registerLazySingleton<GetDoctorAppointmentStatisticsRepositories>(
    () =>
        GetDoctorAppointmentStatisticsRepositoriesImpl(remoteDatasource: sl()),
  );

  // Data sources
  sl.registerLazySingleton<GetAppointmentsByDoctorRemoteDatasource>(
    () => GetAppointmentsByDoctorRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<GetAppointmentDetailsByIDRemoteDatasource>(
    () => GetAppointmentDetailsByIDRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<
    UpdateAppointmentStatusByAppointmentidRemoteDatasource
  >(
    () => UpdateAppointmentStatusByAppointmentidRemoteDatasourceImpl(
      dioClient: sl(),
    ),
  );
  sl.registerLazySingleton<GetDoctorAppointmentStatisticsRemoteDatasource>(
    () => GetDoctorAppointmentStatisticsRemoteDatasourceImpl(dioClient: sl()),
  );
}
