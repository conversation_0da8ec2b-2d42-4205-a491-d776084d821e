import '../../domain/entities/doctor_availability_schedule.dart';

/// Model for doctor availability schedule with JSON serialization
class DoctorAvailabilityScheduleModel extends DoctorAvailabilitySchedule {
  const DoctorAvailabilityScheduleModel({
    required super.startDate,
    required super.endDate,
    required super.isRepeat,
    required super.unavailableDates,
    required super.days,
  });

  /// Creates a model from JSON data
  factory DoctorAvailabilityScheduleModel.fromJson(Map<String, dynamic> json) {
    return DoctorAvailabilityScheduleModel(
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      isRepeat: json['is_repeat'] as bool,
      unavailableDates: (json['unavailable_dates'] as List<dynamic>)
          .map((date) => UnavailableDateModel.fromJson(date as Map<String, dynamic>))
          .toList(),
      days: (json['days'] as List<dynamic>)
          .map((day) => DayScheduleModel.fromJson(day as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Converts the model to JSON format for API requests
  Map<String, dynamic> toJson() {
    return {
      'start_date': _formatDate(startDate),
      'end_date': _formatDate(endDate),
      'is_repeat': isRepeat,
      'unavailable_dates': unavailableDates
          .map((date) => UnavailableDateModel.fromEntity(date).toJson())
          .toList(),
      'days': days
          .map((day) => DayScheduleModel.fromEntity(day).toJson())
          .toList(),
    };
  }

  /// Creates a model from domain entity
  factory DoctorAvailabilityScheduleModel.fromEntity(DoctorAvailabilitySchedule entity) {
    return DoctorAvailabilityScheduleModel(
      startDate: entity.startDate,
      endDate: entity.endDate,
      isRepeat: entity.isRepeat,
      unavailableDates: entity.unavailableDates,
      days: entity.days,
    );
  }

  /// Converts the model to domain entity
  DoctorAvailabilitySchedule toEntity() {
    return DoctorAvailabilitySchedule(
      startDate: startDate,
      endDate: endDate,
      isRepeat: isRepeat,
      unavailableDates: unavailableDates,
      days: days,
    );
  }

  /// Formats DateTime to YYYY-MM-DD string
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// Model for unavailable date with JSON serialization
class UnavailableDateModel extends UnavailableDate {
  const UnavailableDateModel({
    required super.date,
    required super.isFullDay,
    super.timeSlot,
  });

  /// Creates a model from JSON data
  factory UnavailableDateModel.fromJson(Map<String, dynamic> json) {
    return UnavailableDateModel(
      date: DateTime.parse(json['date'] as String),
      isFullDay: json['is_full_day'] as bool,
      timeSlot: json['time_slot'] != null && (json['time_slot'] as Map).isNotEmpty
          ? AvailabilityTimeSlotModel.fromJson(json['time_slot'] as Map<String, dynamic>)
          : null,
    );
  }

  /// Converts the model to JSON format
  Map<String, dynamic> toJson() {
    return {
      'date': _formatDate(date),
      'is_full_day': isFullDay,
      'time_slot': timeSlot != null 
          ? AvailabilityTimeSlotModel.fromEntity(timeSlot!).toJson()
          : {},
    };
  }

  /// Creates a model from domain entity
  factory UnavailableDateModel.fromEntity(UnavailableDate entity) {
    return UnavailableDateModel(
      date: entity.date,
      isFullDay: entity.isFullDay,
      timeSlot: entity.timeSlot,
    );
  }

  /// Converts the model to domain entity
  UnavailableDate toEntity() {
    return UnavailableDate(
      date: date,
      isFullDay: isFullDay,
      timeSlot: timeSlot,
    );
  }

  /// Formats DateTime to YYYY-MM-DD string
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// Model for day schedule with JSON serialization
class DayScheduleModel extends DaySchedule {
  const DayScheduleModel({
    required super.day,
    required super.isOffDay,
    required super.slots,
  });

  /// Creates a model from JSON data
  factory DayScheduleModel.fromJson(Map<String, dynamic> json) {
    return DayScheduleModel(
      day: json['day'] as String,
      isOffDay: json['isOffDay'] as bool,
      slots: (json['slots'] as List<dynamic>)
          .map((slot) => AvailabilityTimeSlotModel.fromJson(slot as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Converts the model to JSON format
  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'isOffDay': isOffDay,
      'slots': slots
          .map((slot) => AvailabilityTimeSlotModel.fromEntity(slot).toJson())
          .toList(),
    };
  }

  /// Creates a model from domain entity
  factory DayScheduleModel.fromEntity(DaySchedule entity) {
    return DayScheduleModel(
      day: entity.day,
      isOffDay: entity.isOffDay,
      slots: entity.slots,
    );
  }

  /// Converts the model to domain entity
  DaySchedule toEntity() {
    return DaySchedule(
      day: day,
      isOffDay: isOffDay,
      slots: slots,
    );
  }
}

/// Model for availability time slot with JSON serialization
class AvailabilityTimeSlotModel extends AvailabilityTimeSlot {
  const AvailabilityTimeSlotModel({
    required super.startTime,
    required super.endTime,
  });

  /// Creates a model from JSON data
  factory AvailabilityTimeSlotModel.fromJson(Map<String, dynamic> json) {
    return AvailabilityTimeSlotModel(
      startTime: json['start_time'] as String,
      endTime: json['end_time'] as String,
    );
  }

  /// Converts the model to JSON format
  Map<String, dynamic> toJson() {
    return {
      'start_time': startTime,
      'end_time': endTime,
    };
  }

  /// Creates a model from domain entity
  factory AvailabilityTimeSlotModel.fromEntity(AvailabilityTimeSlot entity) {
    return AvailabilityTimeSlotModel(
      startTime: entity.startTime,
      endTime: entity.endTime,
    );
  }

  /// Converts the model to domain entity
  AvailabilityTimeSlot toEntity() {
    return AvailabilityTimeSlot(
      startTime: startTime,
      endTime: endTime,
    );
  }
}
