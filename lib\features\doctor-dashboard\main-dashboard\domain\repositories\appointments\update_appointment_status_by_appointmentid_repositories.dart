import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_response.dart';

abstract class UpdateAppointmentStatusByAppointmentidRepositories {
  Future<Either<Failure, UpdateAppointmentStatusByAppointmentidResponseModel>>
  updateAppointmentStatusByAppointmentid(
    UpdateAppointmentStatusByAppointmentidRequestModel request,
  );
}
