import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import '../entities/appointment.dart';
import '../repositories/availability_repository.dart';

class CreateAppointment implements UseCase<Appointment, CreateAppointmentParams> {
  final AvailabilityRepository repository;

  CreateAppointment(this.repository);

  @override
  Future<Either<Failure, Appointment>> call(CreateAppointmentParams params) async {
    return await repository.createAppointment(params.appointment);
  }
}

class CreateAppointmentParams extends Equatable {
  final Appointment appointment;

  const CreateAppointmentParams({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}
