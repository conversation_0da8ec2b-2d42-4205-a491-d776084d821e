import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_response_model.dart';

abstract class GetDoctorAppointmentStatisticsRemoteDatasource {
  Future<GetDoctorAppointmentStatisticsResponseModel>
  getDoctorAppointmentStatistics(
    GetDoctorAppointmentStatisticsRequestModel request,
  );
}

class GetDoctorAppointmentStatisticsRemoteDatasourceImpl
    implements GetDoctorAppointmentStatisticsRemoteDatasource {
  final DioClient dioClient;

  GetDoctorAppointmentStatisticsRemoteDatasourceImpl({required this.dioClient});
  @override
  Future<GetDoctorAppointmentStatisticsResponseModel>
  getDoctorAppointmentStatistics(
    GetDoctorAppointmentStatisticsRequestModel request,
  ) async {
    try {
      // final queryParams = request.toJson();

      // final response = await dioClient.get(
      //   '{{devURL}}/doctors/appointments/statistics',
      // );
      // return GetDoctorAppointmentStatisticsResponseModel.fromJson(response.data);

      await Future.delayed(const Duration(seconds: 2));

      final responseJson = {
        "data": {
          "total_appointments": 3,
          "total_patients": 2,
          "total_clinic_consulting": 2,
          "total_virtual_consulting": 1,
        },
        "message": "Doctor appointment statistics fetched successfully",
        "status": "success",
        "statusCode": 200,
      };

      return GetDoctorAppointmentStatisticsResponseModel.fromJson(responseJson);
    } catch (e) {
      throw ServerException();
    }
  }
}
