import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointment_details_by_id/get_appointment_details_by_id_request.dart';

class GetAppointmentsDetailsByIDRequestModel
    extends GetAppointmentDetailsByIDRequest {
  const GetAppointmentsDetailsByIDRequestModel({required super.appointmentId});

  factory GetAppointmentsDetailsByIDRequestModel.fromEntity(
    GetAppointmentDetailsByIDRequest entity,
  ) {
    return GetAppointmentsDetailsByIDRequestModel(
      appointmentId: entity.appointmentId,
    );
  }

  Map<String, dynamic> toJson() {
    return {"appointmentId": appointmentId};
  }
}
