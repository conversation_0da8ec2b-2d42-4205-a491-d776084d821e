import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import '../entities/doctor_availability_schedule.dart';
import '../repositories/availability_repository.dart';

/// Use case for setting doctor availability schedule
class SetDoctorAvailabilitySchedule implements UseCase<DoctorAvailabilitySchedule, SetDoctorAvailabilityScheduleParams> {
  final AvailabilityRepository repository;

  SetDoctorAvailabilitySchedule(this.repository);

  @override
  Future<Either<Failure, DoctorAvailabilitySchedule>> call(
    SetDoctorAvailabilityScheduleParams params,
  ) async {
    return await repository.setDoctorAvailabilitySchedule(params.schedule);
  }
}

/// Parameters for setting doctor availability schedule
class SetDoctorAvailabilityScheduleParams extends Equatable {
  /// The complete availability schedule to set
  final DoctorAvailabilitySchedule schedule;

  const SetDoctorAvailabilityScheduleParams({
    required this.schedule,
  });

  @override
  List<Object?> get props => [schedule];

  /// Creates params from individual components
  factory SetDoctorAvailabilityScheduleParams.create({
    required DateTime startDate,
    required DateTime endDate,
    required bool isRepeat,
    required List<UnavailableDate> unavailableDates,
    required List<DaySchedule> days,
  }) {
    return SetDoctorAvailabilityScheduleParams(
      schedule: DoctorAvailabilitySchedule(
        startDate: startDate,
        endDate: endDate,
        isRepeat: isRepeat,
        unavailableDates: unavailableDates,
        days: days,
      ),
    );
  }

  /// Creates params from UI state data
  factory SetDoctorAvailabilityScheduleParams.fromUIState({
    required DateTime startDate,
    required DateTime endDate,
    required bool isRepeat,
    required Map<String, bool> dayAvailability,
    required Map<String, List<Map<String, String>>> dayTimeSlots,
    required List<Map<String, dynamic>> unavailableDates,
  }) {
    // Convert unavailable dates
    final unavailableDateEntities = unavailableDates.map((dateData) {
      return UnavailableDate(
        date: dateData['date'] as DateTime,
        isFullDay: dateData['isFullDay'] as bool,
        timeSlot: dateData['timeSlot'] != null
            ? AvailabilityTimeSlot(
                startTime: dateData['timeSlot']['startTime'] as String,
                endTime: dateData['timeSlot']['endTime'] as String,
              )
            : null,
      );
    }).toList();

    // Convert day schedules
    final daySchedules = <DaySchedule>[];
    final daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    
    for (final day in daysOfWeek) {
      final isAvailable = dayAvailability[day.toLowerCase()] ?? false;
      final timeSlots = dayTimeSlots[day.toLowerCase()] ?? [];
      
      final slots = timeSlots.map((slot) {
        return AvailabilityTimeSlot(
          startTime: AvailabilityTimeSlot.convertTo24HourFormat(slot['startTime']!),
          endTime: AvailabilityTimeSlot.convertTo24HourFormat(slot['endTime']!),
        );
      }).toList();

      daySchedules.add(DaySchedule(
        day: day.toLowerCase(),
        isOffDay: !isAvailable,
        slots: slots,
      ));
    }

    return SetDoctorAvailabilityScheduleParams(
      schedule: DoctorAvailabilitySchedule(
        startDate: startDate,
        endDate: endDate,
        isRepeat: isRepeat,
        unavailableDates: unavailableDateEntities,
        days: daySchedules,
      ),
    );
  }
}
