import 'package:equatable/equatable.dart';

class GetAppointmentsByDoctorRequest extends Equatable {
  final String? appointmentType;
  final String? source;
  final String? startDate;
  final String? endDate;
  final int? month;
  final int? year;
  final String? status;
  final int skip;
  final int limit;

  const GetAppointmentsByDoctorRequest({
    this.appointmentType,
    this.source,
    this.startDate,
    this.endDate,
    this.month,
    this.year,
    this.status,
    required this.skip,
    required this.limit,
  });

  @override
  List<Object?> get props => [
    appointmentType,
    source,
    startDate,
    endDate,
    month,
    year,
    status,
    skip,
    limit,
  ];
}
