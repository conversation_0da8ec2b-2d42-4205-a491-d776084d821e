import 'package:equatable/equatable.dart';

class GetDoctorAppointmentStatisticsData extends Equatable {
  final int totalAppointments;
  final int totalPatients;
  final int totalClinicConsulting;
  final int totalVirtualConsulting;

  const GetDoctorAppointmentStatisticsData({
    required this.totalAppointments,
    required this.totalPatients,
    required this.totalClinicConsulting,
    required this.totalVirtualConsulting,
  });

  @override
  List<Object?> get props => [
    totalAppointments,
    totalPatients,
    totalClinicConsulting,
    totalVirtualConsulting,
  ];
}
