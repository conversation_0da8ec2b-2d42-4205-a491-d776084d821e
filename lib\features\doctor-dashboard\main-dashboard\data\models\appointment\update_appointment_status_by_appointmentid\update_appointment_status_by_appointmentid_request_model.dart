import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_request.dart';

class UpdateAppointmentStatusByAppointmentidRequestModel
    extends UpdateAppointmentStatusByAppointmentidRequest {
  const UpdateAppointmentStatusByAppointmentidRequestModel({
    required super.appointmentId,
    required super.status,
  });

  factory UpdateAppointmentStatusByAppointmentidRequestModel.fromEntity(
    UpdateAppointmentStatusByAppointmentidRequest entity,
  ) {
    return UpdateAppointmentStatusByAppointmentidRequestModel(
      appointmentId: entity.appointmentId,
      status: entity.status,
    );
  }

  Map<String, dynamic> toJson() {
    return {"appointmentId": appointmentId, "status": status};
  }
}
