import '../../domain/entities/time_slot.dart';

class TimeSlotModel extends TimeSlot {
  const TimeSlotModel({
    required super.id,
    required super.doctorId,
    required super.startTime,
    required super.endTime,
    required super.isAvailable,
    required super.dayOfWeek,
    required super.date,
  });

  factory TimeSlotModel.fromJson(Map<String, dynamic> json) {
    return TimeSlotModel(
      id: json['id'] as String,
      doctorId: json['doctor_id'] as String,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: DateTime.parse(json['end_time'] as String),
      isAvailable: json['is_available'] as bool,
      dayOfWeek: json['day_of_week'] as String,
      date: DateTime.parse(json['date'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'doctor_id': doctorId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'is_available': isAvailable,
      'day_of_week': dayOfWeek,
      'date': date.toIso8601String(),
    };
  }

  factory TimeSlotModel.fromEntity(TimeSlot timeSlot) {
    return TimeSlotModel(
      id: timeSlot.id,
      doctorId: timeSlot.doctorId,
      startTime: timeSlot.startTime,
      endTime: timeSlot.endTime,
      isAvailable: timeSlot.isAvailable,
      dayOfWeek: timeSlot.dayOfWeek,
      date: timeSlot.date,
    );
  }

  TimeSlot toEntity() {
    return TimeSlot(
      id: id,
      doctorId: doctorId,
      startTime: startTime,
      endTime: endTime,
      isAvailable: isAvailable,
      dayOfWeek: dayOfWeek,
      date: date,
    );
  }
}
