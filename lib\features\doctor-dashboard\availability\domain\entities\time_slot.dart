import 'package:equatable/equatable.dart';

class TimeSlot extends Equatable {
  final String id;
  final String doctorId;
  final DateTime startTime;
  final DateTime endTime;
  final bool isAvailable;
  final String dayOfWeek;
  final DateTime date;

  const TimeSlot({
    required this.id,
    required this.doctorId,
    required this.startTime,
    required this.endTime,
    required this.isAvailable,
    required this.dayOfWeek,
    required this.date,
  });

  @override
  List<Object?> get props => [
        id,
        doctorId,
        startTime,
        endTime,
        isAvailable,
        dayOfWeek,
        date,
      ];

  TimeSlot copyWith({
    String? id,
    String? doctorId,
    DateTime? startTime,
    DateTime? endTime,
    bool? isAvailable,
    String? dayOfWeek,
    DateTime? date,
  }) {
    return TimeSlot(
      id: id ?? this.id,
      doctorId: doctorId ?? this.doctorId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isAvailable: isAvailable ?? this.isAvailable,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      date: date ?? this.date,
    );
  }
}
