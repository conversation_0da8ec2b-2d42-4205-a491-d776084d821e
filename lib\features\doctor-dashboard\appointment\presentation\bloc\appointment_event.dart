part of 'appointment_bloc.dart';

@immutable
abstract class AppointmentEvent {}

/// ---- List / Operations ----
class LoadAppointmentsEvent extends AppointmentEvent {}

class RefreshAppointmentsEvent extends AppointmentEvent {}

class CancelAppointmentEvent extends AppointmentEvent {
  final String appointmentId;
  CancelAppointmentEvent(this.appointmentId);
}

class ConfirmAppointmentEvent extends AppointmentEvent {
  final String appointmentId;
  ConfirmAppointmentEvent(this.appointmentId);
}

/// ---- Details ----
class FetchAppointmentDetailEvent extends AppointmentEvent {
  final String appointmentId;
  FetchAppointmentDetailEvent(this.appointmentId);
}

/// ---- Create New ----
class CreateNewAppointmentEvent extends AppointmentEvent {
  final NewAppointmentRequest request;
  CreateNewAppointmentEvent(this.request);
}
