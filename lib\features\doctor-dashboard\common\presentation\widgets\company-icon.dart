import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';

class CompanyIcon extends StatefulWidget {
  final double height;
  final double width;
  final bool onlyIcon;
  final bool onlyText;
  final MainAxisAlignment spacingType;
  final double padding;
  const CompanyIcon({
    super.key,
    this.height = 20,
    this.width = 20,
    this.onlyIcon = false,
    this.onlyText = false,
    this.spacingType = MainAxisAlignment.center,
    this.padding = 7.5,
  });

  @override
  State<CompanyIcon> createState() => _CompanyIconState();
}

class _CompanyIconState extends State<CompanyIcon> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: widget.padding),
      child: Row(
        mainAxisAlignment: widget.spacingType,
        spacing: 10,
        children: [
          if (!widget.onlyText)
            ReusableSvgImage(
              assetPath: 'assets/icons/doctor/imed-logo.svg',
              height: widget.height,
            ),
          if (widget.onlyIcon && widget.onlyText)
            const SizedBox(width: AppConstants.sizedBoxHeightSmall),
          if (!widget.onlyIcon)
            ReusableSvgImage(
              assetPath: 'assets/icons/doctor/imed-text-logo.svg',
              height: widget.height,
            ),
        ],
      ),
    );
  }
}
