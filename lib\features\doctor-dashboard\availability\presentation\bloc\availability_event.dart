import 'package:equatable/equatable.dart';
import '../../domain/entities/doctor_availability_schedule.dart';
import '../../domain/entities/appointment.dart';

abstract class AvailabilityEvent extends Equatable {
  const AvailabilityEvent();

  @override
  List<Object?> get props => [];
}

class LoadDoctorAvailability extends AvailabilityEvent {
  final String doctorId;
  final DateTime date;

  const LoadDoctorAvailability({required this.doctorId, required this.date});

  @override
  List<Object?> get props => [doctorId, date];
}

class LoadAvailableTimeSlots extends AvailabilityEvent {
  final String doctorId;
  final DateTime date;

  const LoadAvailableTimeSlots({required this.doctorId, required this.date});

  @override
  List<Object?> get props => [doctorId, date];
}

class CreateAppointmentEvent extends AvailabilityEvent {
  final Appointment appointment;

  const CreateAppointmentEvent({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

class SelectTimeSlot extends AvailabilityEvent {
  final String timeSlotId;

  const SelectTimeSlot({required this.timeSlotId});

  @override
  List<Object?> get props => [timeSlotId];
}

class UpdateAppointmentForm extends AvailabilityEvent {
  final String? doctorId;
  final String? patientName;
  final int? age;
  final String? appointmentType;
  final String? complaintType;
  final String? reason;

  const UpdateAppointmentForm({
    this.doctorId,
    this.patientName,
    this.age,
    this.appointmentType,
    this.complaintType,
    this.reason,
  });

  @override
  List<Object?> get props => [
    doctorId,
    patientName,
    age,
    appointmentType,
    complaintType,
    reason,
  ];
}

class ResetAvailabilityState extends AvailabilityEvent {
  const ResetAvailabilityState();
}

class LoadPatientAppointments extends AvailabilityEvent {
  final String patientId;

  const LoadPatientAppointments({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

class LoadDoctorAppointments extends AvailabilityEvent {
  final String doctorId;
  final DateTime? date;

  const LoadDoctorAppointments({required this.doctorId, this.date});

  @override
  List<Object?> get props => [doctorId, date];
}

class UpdateAppointmentStatusEvent extends AvailabilityEvent {
  final String appointmentId;
  final String status;

  const UpdateAppointmentStatusEvent({
    required this.appointmentId,
    required this.status,
  });

  @override
  List<Object?> get props => [appointmentId, status];
}

class CancelAppointmentEvent extends AvailabilityEvent {
  final String appointmentId;

  const CancelAppointmentEvent({required this.appointmentId});

  @override
  List<Object?> get props => [appointmentId];
}

class SetDoctorAvailability extends AvailabilityEvent {
  final String doctorId;
  final DateTime startDate;
  final DateTime endDate;
  final String startTime;
  final String endTime;
  final List<String> availableDays;
  final bool repeatAvailability;

  const SetDoctorAvailability({
    required this.doctorId,
    required this.startDate,
    required this.endDate,
    required this.startTime,
    required this.endTime,
    required this.availableDays,
    required this.repeatAvailability,
  });

  @override
  List<Object?> get props => [
    doctorId,
    startDate,
    endDate,
    startTime,
    endTime,
    availableDays,
    repeatAvailability,
  ];
}

class MarkUnavailableDates extends AvailabilityEvent {
  final String doctorId;
  final List<DateTime> unavailableDates;

  const MarkUnavailableDates({
    required this.doctorId,
    required this.unavailableDates,
  });

  @override
  List<Object?> get props => [doctorId, unavailableDates];
}

class UpdateDayAvailability extends AvailabilityEvent {
  final String doctorId;
  final String dayOfWeek;
  final bool isAvailable;
  final List<TimeSlotData>? timeSlots;

  const UpdateDayAvailability({
    required this.doctorId,
    required this.dayOfWeek,
    required this.isAvailable,
    this.timeSlots,
  });

  @override
  List<Object?> get props => [doctorId, dayOfWeek, isAvailable, timeSlots];
}

class AddTimeSlot extends AvailabilityEvent {
  final String doctorId;
  final String dayOfWeek;
  final String startTime;
  final String endTime;

  const AddTimeSlot({
    required this.doctorId,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
  });

  @override
  List<Object?> get props => [doctorId, dayOfWeek, startTime, endTime];
}

class RemoveTimeSlot extends AvailabilityEvent {
  final String doctorId;
  final String dayOfWeek;
  final String timeSlotId;

  const RemoveTimeSlot({
    required this.doctorId,
    required this.dayOfWeek,
    required this.timeSlotId,
  });

  @override
  List<Object?> get props => [doctorId, dayOfWeek, timeSlotId];
}

/// Event for saving doctor availability schedule
class SaveDoctorAvailabilitySchedule extends AvailabilityEvent {
  final DoctorAvailabilitySchedule schedule;

  const SaveDoctorAvailabilitySchedule({required this.schedule});

  @override
  List<Object?> get props => [schedule];
}

// Helper class for time slot data in events
class TimeSlotData {
  final String id;
  final String startTime;
  final String endTime;

  const TimeSlotData({
    required this.id,
    required this.startTime,
    required this.endTime,
  });
}
