import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';

import 'package:imed_fe/features/doctor-dashboard/availability/router/availability_router.dart';

import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/pages/mainpage.dart';

class AppRouter {
  static const String dashboard = '/dashboard';
  static const String myProfile = '/my-profile';
  static const String myProfileEdit = 'personal-info/edit';
  static const String myProfileWorkplaceEdit = 'workplace-info/edit';
  static const String myProfileWorkplaceAdd = 'workplace-info/add';
  static const String myProfileProfileExperienceEdit =
      'profile-experience/edit';
  static const String myProfileProfileExperienceAdd = 'profile-experience/add';
  static const String calendar = '/calendar';
  static const String patientsList = '/patients-list';
  static const String patientsDetailsReport = '/patients-details-report';
  static const String addPatient = '/patients/add';
  static const String patientDetails = '/patients/:patientId';
  static const String consultationDetails =
      '/patients/:patientId/consultations/:consultationId';
  static const String payment = '/payment';
  static const String invoice = '/invoice';
  static const String patients = '/patients';

  static const String availability = '/availability';

  static const String newAppointment = '/new-appointment';
  static const String appointmentDetails = '/appointment-details/:id';
  static const String appointments = '/appointments';
  static const String patientConsultation = '/patient-consultation';
  static const String patientDetailsPage = '/patient-details-page';

  static bool isCurrentRoute(BuildContext context, String path) {
    final location = GoRouterState.of(context).matchedLocation;
    return location == path;
  }

  // Helper methods to get full paths
  static String getFullPath(String path) => '/$path';
  static String getProfilePath(String subPath) => '$myProfile/$subPath';

  static final GoRouter router = GoRouter(
    initialLocation: availability,
    routes: [
      ShellRoute(
        builder: (BuildContext context, GoRouterState state, Widget child) {
          return MainPage(child: child);
        },
        routes: [...availabilityRoute],
      ),
    ],
  );
}
