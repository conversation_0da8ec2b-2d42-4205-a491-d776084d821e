import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_response_model.dart';

abstract class GetDoctorAppointmentStatisticsRepositories {
  Future<Either<Failure, GetDoctorAppointmentStatisticsResponseModel>>
  getDoctorAppointmentStatistics(
    GetDoctorAppointmentStatisticsRequestModel request,
  );
}
