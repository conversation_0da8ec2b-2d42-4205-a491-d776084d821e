import 'dart:developer' as developer;
import 'package:dio/dio.dart';
import 'package:imed_fe/core/security/storage/token_storage.dart';

class TokenModel {
  final String accessToken;
  final String? refreshToken;
  final DateTime expiresAt;

  TokenModel({
    required this.accessToken,
    this.refreshToken,
    required this.expiresAt,
  });

  factory TokenModel.fromAzureResponse(Map<String, dynamic> json) {
    final expiresIn = json['expires_in'] as int? ?? 3600;
    return TokenModel(
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String?,
      expiresAt: DateTime.now().add(Duration(seconds: expiresIn)),
    );
  }
}

class TokenService {
  final TokenStorage _storage;
  final Dio _dio;
  final String azureClientId;
  final String azureClientSecret;
  final String azureTokenEndpoint;

  TokenService({
    required TokenStorage storage,
    required Dio dio,
    required this.azureClientId,
    required this.azureClientSecret,
    required this.azureTokenEndpoint,
  }) : _storage = storage,
       _dio = dio;

  Future<void> saveTokens(TokenModel tokenModel) async {
    await _storage.saveTokens(
      accessToken: tokenModel.accessToken,
      refreshToken: tokenModel.refreshToken,
      expiresAt: tokenModel.expiresAt,
    );
    developer.log('✅ Tokens saved', name: 'TokenService', level: 800);
  }

  Future<bool> validateAndRefreshToken() async {
    if (await _storage.isTokenExpired()) {
      developer.log(
        '⚠️ Token expired, refreshing...',
        name: 'TokenService',
        level: 900,
      );
      return await _refreshToken();
    }
    return true;
  }

  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _storage.getRefreshToken();
      if (refreshToken == null) {
        developer.log('❌ No refresh token', name: 'TokenService', level: 1000);
        return false;
      }

      developer.log(
        '🔄 Refreshing token from Azure...',
        name: 'TokenService',
        level: 800,
      );

      final response = await _dio
          .post(
            azureTokenEndpoint,
            data: {
              'grant_type': 'refresh_token',
              'refresh_token': refreshToken,
              'client_id': azureClientId,
              'client_secret': azureClientSecret,
            },
            options: Options(contentType: Headers.formUrlEncodedContentType),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final tokenModel = TokenModel.fromAzureResponse(response.data);
        await _storage.saveTokens(
          accessToken: tokenModel.accessToken,
          refreshToken: tokenModel.refreshToken ?? refreshToken,
          expiresAt: tokenModel.expiresAt,
        );
        developer.log('✅ Token refreshed', name: 'TokenService', level: 800);
        return true;
      }
      developer.log(
        '❌ Refresh failed: ${response.statusCode}',
        name: 'TokenService',
        level: 1000,
      );
      return false;
    } catch (e) {
      developer.log('❌ Refresh error: $e', name: 'TokenService', level: 1000);
      return false;
    }
  }

  Future<String?> getAccessToken() async {
    return await _storage.getAccessToken();
  }

  Future<bool> isAuthenticated() async {
    final token = await _storage.getAccessToken();
    final isExpired = await _storage.isTokenExpired();
    return token != null && !isExpired;
  }

  Future<void> logout() async {
    await _storage.clearTokens();
    developer.log('✅ Logged out', name: 'TokenService', level: 800);
  }

  Future<void> printTokenInfo() async {
    await _storage.printTokenInfo();
  }
}
