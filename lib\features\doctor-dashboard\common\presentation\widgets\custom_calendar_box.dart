import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/widgets/labelled-widget.dart';

class CustomCalendarBox extends StatefulWidget {
  final String? hintText;
  final String? label;
  final DateTime? value;
  final bool withTimePicker;
  final bool isRequired;
  final ValueChanged<DateTime>? onChanged;
  const CustomCalendarBox({
    super.key,
    this.hintText,
    this.label,
    this.value,
    this.withTimePicker = false,
    this.isRequired = false,
    this.onChanged,
  });

  @override
  State<CustomCalendarBox> createState() => _CustomCalendarBoxState();
}

class _CustomCalendarBoxState extends State<CustomCalendarBox> {
  late String formattedDate;

  @override
  void initState() {
    super.initState();
    if (widget.value != null) {
      formattedDate =
          "${widget.value!.day}/${widget.value!.month}/${widget.value!.year}";
    } else {
      formattedDate = "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return LabelledWidget(
      label: widget.label,
      isRequired: widget.isRequired,
      border: BorderSide(
        style: BorderStyle.solid,
        color: AppConstants.borderColor,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: MouseRegion(
        cursor: WidgetStateMouseCursor.clickable,
        child: GestureDetector(
          onTap: () {
            showDatePicker(
              context: context,
              initialDate: widget.value ?? DateTime.now(),
              firstDate: DateTime(1900),
              lastDate: DateTime.now(),
            ).then((selectedDate) {
              if (selectedDate != null) {
                setState(() {
                  formattedDate =
                      "${selectedDate.day}/${selectedDate.month}/${selectedDate.year}";
                  widget.onChanged?.call(selectedDate);
                });
              }
            });
          },
          child: Container(
            color: Colors.transparent,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  formattedDate.isEmpty
                      ? widget.hintText ?? "Select Date"
                      : formattedDate,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color:
                        formattedDate.isEmpty
                            ? AppConstants.inputFieldForegroundColor.withValues(
                              alpha: 0.6,
                            )
                            : AppConstants.inputFieldForegroundColor,
                  ),
                ),

                ReusableSvgImage(
                  assetPath: "assets/icons/doctor/calendar-icon.svg",
                  color: AppConstants.inputFieldForegroundColor,
                  width: 20,
                  height: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
