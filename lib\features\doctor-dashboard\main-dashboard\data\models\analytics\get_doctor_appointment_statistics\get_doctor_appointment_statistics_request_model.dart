import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_request.dart';

class GetDoctorAppointmentStatisticsRequestModel
    extends GetDoctorAppointmentStatisticsRequest {
  GetDoctorAppointmentStatisticsRequestModel();

  factory GetDoctorAppointmentStatisticsRequestModel.fromEntity(
    GetDoctorAppointmentStatisticsRequest entity,
  ) {
    return GetDoctorAppointmentStatisticsRequestModel();
  }

  Map<String, dynamic> toJson() {
    return {};
  }
}
