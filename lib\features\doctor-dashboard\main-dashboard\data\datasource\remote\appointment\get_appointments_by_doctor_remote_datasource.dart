import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_by_doctor/get_appointments_by_doctor_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_by_doctor/get_appointments_by_doctor_response_model.dart';

abstract class GetAppointmentsByDoctorRemoteDatasource {
  Future<GetAppointmentsByDoctorResponseModel> getAppointmentsByDoctor(
    GetAppointmentsByDoctorRequestModel request,
  );
}

class GetAppointmentsByDoctorRemoteDatasourceImpl
    implements GetAppointmentsByDoctorRemoteDatasource {
  final DioClient dioClient;

  GetAppointmentsByDoctorRemoteDatasourceImpl({required this.dioClient});

  @override
  Future<GetAppointmentsByDoctorResponseModel> getAppointmentsByDoctor(
    GetAppointmentsByDoctorRequestModel request,
  ) async {
    try {
      // final queryParams = request.toJson();

      // final response = await dioClient.get(
      //   '/appointments/by-doctor',
      //   queryParameters: queryParams,
      // );
      // return GetAppointmentsByDoctorResponseModel.fromJson(response.data);

      // await Future.delayed(const Duration(seconds: 2));

      final responseJson = {
        "data": [
          {
            "time": "10:00:00",
            "booking_id": "APT001",
            "appointment_id": "appt-1",
            "disease": "Regular checkup for chest pain",
            "phone": "+1234567890",
            "status": "confirmed",
          },
          {
            "time": "10:30:00",
            "booking_id": "APT002",
            "disease": "Follow-up consultation for previous symptoms",
            "phone": "+1234567891",
            "status": "requested",
          },
          {
            "time": "11:30:00",
            "booking_id": "APT007",
            "appointment_id": "appt-2",
            "disease": "Hypertension management consultation",
            "phone": "+1234567896",
            "status": "confirmed",
          },
          {
            "time": "13:00:00",
            "booking_id": "APT003",
            "appointment_id": "appt-3",
            "disease": "Emergency consultation for heart palpitations",
            "phone": "+1234567892",
            "status": "completed",
          },
        ],
        "message": "Appointments fetched successfully",
        "status": "success",
        "statusCode": 200,
      };

      return GetAppointmentsByDoctorResponseModel.fromJson(responseJson);
    } catch (e) {
      throw ServerException();
    }
  }
}
