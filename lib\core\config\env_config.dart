import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvConfig {
  // Base API Configuration
  static String get apiBaseUrl => dotenv.env['API_BASE_URL'] ?? '';
  static String get apiTimeout => dotenv.env['API_TIMEOUT'] ?? '30';
  static String get environment => dotenv.env['ENVIRONMENT'] ?? 'development';

  // Doctor Profile & Basic Info
  static String get doctorProfileEndpoint => '$apiBaseUrl/doctors/profile';
  static String get profileCompletionStatusEndpoint =>
      '$apiBaseUrl/doctors/profile/completion-status';
  static String get profileImageUploadEndpoint =>
      '$apiBaseUrl/doctors/profile/image';

  // Departments
  static String get departmentsEndpoint => '$apiBaseUrl/doctors/departments';

  // Specialities
  static String get specialitiesEndpoint => '$apiBaseUrl/doctors/specialities';

  // Addresses
  static String get addressesEndpoint => '$apiBaseUrl/doctors/addresses';

  // Hospitals
  static String get hospitalsEndpoint => '$apiBaseUrl/doctors/hospitals';

  // Experiences
  static String get experiencesEndpoint => '$apiBaseUrl/doctors/experiences';

  // Workplaces
  static String get workplacesEndpoint => '$apiBaseUrl/doctors/workplaces';

  // Consultation Settings
  static String get consultationSettingsEndpoint =>
      '$apiBaseUrl/doctors/consultation-settings';

  // Availability & Scheduling
  static String get availabilityEndpoint => '$apiBaseUrl/doctors/availability';
  static String get availableSlotDatesEndpoint =>
      '$apiBaseUrl/doctors/available-slots/dates';
  static String get availableSlotTimesEndpoint =>
      '$apiBaseUrl/doctors/available-slots/times';
  static String get autoRepeatAvailabilityEndpoint =>
      '$apiBaseUrl/doctors/availability/auto-repeat';

  // Credentials
  static String get credentialsEndpoint => '$apiBaseUrl/doctors/credentials';

  // Bank Details
  static String get bankDetailsEndpoint => '$apiBaseUrl/doctors/bank-details';

  // Investigations
  static String get investigationsEndpoint =>
      '$apiBaseUrl/doctors/investigations';

  // Ailments
  static String get ailmentsEndpoint => '$apiBaseUrl/doctors/ailments';

  // Complaints
  static String get complaintsEndpoint => '$apiBaseUrl/doctors/complaints';

  // Appointments
  static String get appointmentsEndpoint => '$apiBaseUrl/doctors/appointments';
  static String get appointmentsCalendarEndpoint =>
      '$apiBaseUrl/doctors/appointments/calendar';

  // Patients
  static String get patientsEndpoint => '$apiBaseUrl/doctors/patients';
  static String get walkInPatientEndpoint =>
      '$apiBaseUrl/doctors/patients/walk-in';

  // Prescriptions
  static String get prescriptionsEndpoint =>
      '$apiBaseUrl/doctors/prescriptions';
  static String get prescriptionInvoiceEndpoint =>
      '$apiBaseUrl/doctors/prescriptions/invoice';

  // Prescription Items
  static String get prescriptionItemsEndpoint =>
      '$apiBaseUrl/doctors/prescriptions/items';

  // Patient Vitals
  static String get patientVitalsEndpoint => '$apiBaseUrl/doctors/vitals';
  static String get prescriptionVitalsEndpoint =>
      '$apiBaseUrl/doctors/prescriptions/vitals';

  // Patient Investigations
  static String get patientInvestigationsEndpoint =>
      '$apiBaseUrl/doctors/patient-investigations';
  static String get prescriptionInvestigationsEndpoint =>
      '$apiBaseUrl/doctors/prescriptions/investigations';

  // Patient Ailments
  static String get patientAilmentsEndpoint =>
      '$apiBaseUrl/doctors/patient-ailments';
  static String get prescriptionAilmentsEndpoint =>
      '$apiBaseUrl/doctors/prescriptions/ailments';

  // Patient Reports
  static String get patientReportsEndpoint =>
      '$apiBaseUrl/doctors/patient-reports';

  // Reviews
  static String get reviewsEndpoint => '$apiBaseUrl/doctors/reviews';
}
