<Column>
  <PwInvoiceDetails invoiceNumber="{invoice.invoiceCode}" date="{invoice.generatedOn}" />

  <SizedBox height="12" />

  <Row crossAxisAlignment="start">
    <Expanded>
      <PwPatientInfo
        patientName="{invoice.patient.name}"
        patientGender="{invoice.patient.gender}"
        patientAge="{invoice.patient.age}"
        patientCondition="Brain MRI Scan (Needs to be dynamic)"
        patientContactNumber="{invoice.patient.phone}" />
    </Expanded>

    <SizedBox width="12" />

    <Expanded>
      <PwDoctorInfo
        doctorName="{invoice.doctor.name}"
        doctorContactNumber="{invoice.doctor.phone}"
        hospitalName="{invoice.doctor.workplace}"
        doctorSpecialization="{invoice.doctor.category}"
        hospitalSvg="{hospitalSvg}" />
    </Expanded>
  </Row>

  <SizedBox height="12" />

  <PwPaidSection amount="{invoice.payment.amount}" date="{invoice.payment.paidOn}" />

  <SizedBox height="12" />

  <PwDoctorServicesTable appointment="{invoice.appointment}" payment="{invoice.payment}" />

  <SizedBox height="12" />

  <PwDoctorWellness />

  <SizedBox height="12" />

  <PwDoctorInitials
    doctorName="{invoice.doctor.name}"
    doctorContact="{invoice.doctor.phone}"
    doctorEmail="{invoice.doctor.email}" />
</Column>
