import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/appointment/update_appointment_status_by_appointmentid_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_response.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/update_appointment_status_by_appointmentid_repositories.dart';

class UpdateAppointmentStatusByAppointmentidRepositoriesImpl
    implements UpdateAppointmentStatusByAppointmentidRepositories {
  final UpdateAppointmentStatusByAppointmentidRemoteDatasource remoteDatasource;

  UpdateAppointmentStatusByAppointmentidRepositoriesImpl({
    required this.remoteDatasource,
  });

  @override
  Future<Either<Failure, UpdateAppointmentStatusByAppointmentidResponseModel>>
  updateAppointmentStatusByAppointmentid(
    UpdateAppointmentStatusByAppointmentidRequestModel request,
  ) async {
    try {
      final response = await remoteDatasource
          .updateAppointmentStatusByAppointmentid(request);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
