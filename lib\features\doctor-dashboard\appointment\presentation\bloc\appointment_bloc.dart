import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import '../../data/models/appointment_detail_model.dart';
import '../../domain/entities/appointment_entity.dart';
import '../../domain/entities/new_appointment_request.dart';
import '../../domain/usecases/cancel_appointment_usecase.dart';
import '../../domain/usecases/confirm_appointment_usecase.dart';
import '../../domain/usecases/get_appointment_detail_usecase.dart';
import '../../domain/usecases/get_appointment_usecase.dart';
import '../../domain/usecases/new_appointment_usecase.dart';

part 'appointment_event.dart';
part 'appointment_state.dart';

class AppointmentBloc extends Bloc<AppointmentEvent, AppointmentState> {
  final GetAppointmentsUseCase getAppointmentsUseCase;
  final CancelAppointmentUseCase cancelAppointmentUseCase;
  final ConfirmAppointmentUseCase confirmAppointmentUseCase;
  final GetAppointmentDetailUsecase getAppointmentDetailUsecase;
  final NewAppointmentUsecase newAppointmentUsecase;

  AppointmentBloc({
    required this.getAppointmentsUseCase,
    required this.cancelAppointmentUseCase,
    required this.confirmAppointmentUseCase,
    required this.getAppointmentDetailUsecase,
    required this.newAppointmentUsecase,
  }) : super(AppointmentInitial()) {
    // Appointment list
    on<LoadAppointmentsEvent>(_onLoadAppointments);
    on<RefreshAppointmentsEvent>(_onRefreshAppointments);
    on<CancelAppointmentEvent>(_onCancelAppointment);
    on<ConfirmAppointmentEvent>(_onConfirmAppointment);

    // Appointment details
    on<FetchAppointmentDetailEvent>(_onFetchAppointmentDetail);

    // New appointment
    on<CreateNewAppointmentEvent>(_onCreateNewAppointment);
  }

  // ======== List / CRUD =========
  Future<void> _onLoadAppointments(
      LoadAppointmentsEvent event, Emitter<AppointmentState> emit) async {
    emit(AppointmentLoading());
    try {
      final appointments = await getAppointmentsUseCase();
      if (appointments.isEmpty) {
        emit(AppointmentEmpty());
      } else {
        emit(AppointmentLoaded(appointments));
      }
    } catch (e) {
      emit(AppointmentError('Failed to load appointments: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshAppointments(
      RefreshAppointmentsEvent event, Emitter<AppointmentState> emit) async {
    final currentState = state;
    if (currentState is AppointmentLoaded) {
      emit(AppointmentOperationLoading(currentState.appointments));
      try {
        final appointments = await getAppointmentsUseCase();
        emit(AppointmentLoaded(appointments));
      } catch (e) {
        emit(AppointmentError('Failed to refresh appointments: ${e.toString()}'));
        // Revert to previous state on error
        emit(currentState);
      }
    }
  }

  Future<void> _onCancelAppointment(
      CancelAppointmentEvent event, Emitter<AppointmentState> emit) async {
    final currentState = state;
    if (currentState is AppointmentLoaded) {
      emit(AppointmentOperationLoading(currentState.appointments));
      try {
        await cancelAppointmentUseCase(event.appointmentId);
        final appointments = await getAppointmentsUseCase();
        emit(AppointmentOperationSuccess(
          message: 'Appointment cancelled successfully',
          appointments: appointments,
        ));
      } catch (e) {
        emit(AppointmentError('Failed to cancel appointment: ${e.toString()}'));
        // Revert to previous state on error
        emit(currentState);
      }
    }
  }

  Future<void> _onConfirmAppointment(
      ConfirmAppointmentEvent event, Emitter<AppointmentState> emit) async {
    final currentState = state;
    if (currentState is AppointmentLoaded) {
      emit(AppointmentOperationLoading(currentState.appointments));
      try {
        await confirmAppointmentUseCase(event.appointmentId);
        final appointments = await getAppointmentsUseCase();
        emit(AppointmentOperationSuccess(
          message: 'Appointment confirmed successfully',
          appointments: appointments,
        ));
      } catch (e) {
        emit(AppointmentError('Failed to confirm appointment: ${e.toString()}'));
        // Revert to previous state on error
        emit(currentState);
      }
    }
  }

  // ======== Details =========
  Future<void> _onFetchAppointmentDetail(
      FetchAppointmentDetailEvent event, Emitter<AppointmentState> emit) async {
    emit(AppointmentDetailLoading());
    try {
      final appointmentDetail = await getAppointmentDetailUsecase(event.appointmentId);
      emit(AppointmentDetailLoaded(appointmentDetail));
    } catch (e) {
      emit(AppointmentError('Failed to load appointment detail: ${e.toString()}'));
    }
  }

  // ======== Create =========
  Future<void> _onCreateNewAppointment(
      CreateNewAppointmentEvent event, Emitter<AppointmentState> emit) async {
    emit(NewAppointmentLoading());
    try {
      final appointment = await newAppointmentUsecase(event.request);
      emit(NewAppointmentSuccess(appointment));
    } catch (e) {
      emit(NewAppointmentFailure(e.toString()));
    }
  }
}