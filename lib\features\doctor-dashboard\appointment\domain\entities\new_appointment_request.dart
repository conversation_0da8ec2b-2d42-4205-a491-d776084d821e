class NewAppointmentRequest {
  final String firstName;
  final String lastName;
  final String email;
  final String countryCode;
  final String phone;
  final String bloodGroup;
  final String gender;
  final String date;
  final String timeSlot;
  final String complaints;
  final String problemDescription;
  // Note: Uploads are typically handled as File objects, omitted for simplicity

  NewAppointmentRequest({
    required this.firstName,
    this.lastName = '',
    this.email = '',
    required this.countryCode,
    required this.phone,
    required this.bloodGroup,
    required this.gender,
    required this.date,
    required this.timeSlot,
    required this.complaints,
    this.problemDescription = '',
  });

  // Method to convert the entity to a map for the repository/API call
  Map<String, dynamic> toJson() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'country_code': countryCode,
      'phone': phone,
      'blood_group': bloodGroup,
      'gender': gender,
      'appointment_date': date,
      'appointment_time_slot': timeSlot,
      'complaints': complaints,
      'problem_description': problemDescription,
      // ... add other fields as needed for the API
    };
  }
}