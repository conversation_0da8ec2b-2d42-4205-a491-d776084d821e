import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointment_details_by_id/get_appointment_details_by_id_data.dart';

abstract class DashboardState extends Equatable {
  @override
  List<Object?> get props => [];
}

class DashboardInitial extends DashboardState {}

class DashboardLoading extends DashboardState {}

class TodayAppointmentsState extends DashboardState {
  final List<GetAppointmentDetailsByIDData> appointments;
  final bool isLoading;
  final String? error;

  TodayAppointmentsState({
    this.appointments = const [],
    this.isLoading = false,
    this.error,
  });
}

class AppointmentRequestsState extends DashboardState {
  final List<GetAppointmentDetailsByIDData> appointments;
  final bool isLoading;
  final String? error;

  AppointmentRequestsState({
    this.appointments = const [],
    this.isLoading = false,
    this.error,
  });
}

class RecentPatientsState extends DashboardState {
  final List<GetAppointmentDetailsByIDData> appointments;
  final bool isLoading;
  final String? error;

  RecentPatientsState({
    this.appointments = const [],
    this.isLoading = false,
    this.error,
  });
}

class PatientRecordState extends DashboardState {
  final int currentPatientCount;
  final int previousPatientCount;
  final double previousPatientRatio;
  final double currentPatientRatio;
  final bool isLoading;
  final String? error;

  PatientRecordState({
    this.currentPatientCount = 0,
    this.previousPatientCount = 0,
    this.previousPatientRatio = 0.0,
    this.currentPatientRatio = 0.0,
    this.isLoading = false,
    this.error,
  });
}

class DashboardLoaded extends DashboardState {
  final TodayAppointmentsState todayAppointments;
  final AppointmentRequestsState appointmentRequests;
  final RecentPatientsState recentPatients;
  final PatientRecordState patientRecord;
  final String? updatingAppointmentId;

  DashboardLoaded({
    required this.todayAppointments,
    required this.appointmentRequests,
    required this.recentPatients,
    required this.patientRecord,
    this.updatingAppointmentId,
  });

  DashboardLoaded copyWith({
    TodayAppointmentsState? todayAppointments,
    AppointmentRequestsState? appointmentRequests,
    RecentPatientsState? recentPatients,
    PatientRecordState? patientRecord,
    String? updatingAppointmentId,
  }) {
    return DashboardLoaded(
      todayAppointments: todayAppointments ?? this.todayAppointments,
      appointmentRequests: appointmentRequests ?? this.appointmentRequests,
      recentPatients: recentPatients ?? this.recentPatients,
      patientRecord: patientRecord ?? this.patientRecord,
      updatingAppointmentId:
          updatingAppointmentId ?? this.updatingAppointmentId,
    );
  }

  @override
  List<Object?> get props => [
    todayAppointments,
    appointmentRequests,
    recentPatients,
    patientRecord,
    updatingAppointmentId,
  ];
}

class DashboardError extends DashboardState {
  final String message;
  final String? status;

  DashboardError({required this.message, this.status});

  @override
  List<Object?> get props => [message];
}
