import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointment_details_by_id/get_appointment_details_by_id_data.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointment_details_by_id/get_appointment_details_by_id_response.dart';

class GetAppointmentDetailsByIDResponseModel
    extends GetAppointmentDetailsByIDResponse {
  const GetAppointmentDetailsByIDResponseModel({
    required super.data,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory GetAppointmentDetailsByIDResponseModel.fromJson(
    Map<String, dynamic> json,
  ) {
    return GetAppointmentDetailsByIDResponseModel(
      data: GetAppointmentDetailsByIDData(
        appointmentId: json['data']['appointment_id'] as String,
        patientName: json['data']['patient_name'] as String,
        age: json['data']['age'] as int,
        gender: json['data']['gender'] as String,
        patientPhone: json['data']['patient_phone'] as String,
        appointmentType: json['data']['appointment_type'] as String,
        complaints:
            (json['data']['complaints'] as List<dynamic>)
                .map(
                  (complaint) => Complaint(
                    label: complaint['label'] as String,
                    value: complaint['value'] as String,
                  ),
                )
                .toList(),
        status: json['data']['status'] as String,
        reason: json['data']['reason'] as String,
        date: json['data']['date'] as String,
        startTime: json['data']['start_time'] as String,
        endTime: json['data']['end_time'] as String,
        files:
            (json['data']['files'] as List<dynamic>)
                .map(
                  (file) =>
                      AppointmentFile(fileName: file['file_name'] as String),
                )
                .toList(),
      ),

      message: json['message'] as String,
      status: json['status'] as String,
      statusCode: json['statusCode'] as int,
    );
  }
}
