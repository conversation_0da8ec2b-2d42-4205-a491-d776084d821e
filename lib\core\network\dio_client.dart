import 'package:dio/dio.dart';
import 'package:imed_fe/core/config/env_config.dart';
import 'package:imed_fe/core/security/auth_interceptor.dart';
import 'package:imed_fe/core/security/services/token_service.dart';

class DioClient {
  final Dio _dio;

  // Hardcoded token option as requested
  static const String? _hardcodedToken =
      'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Im1VRzZ2WW5SbG9mdUx4Y2lacnU3U3B0ZjZOTSJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.geahFGDgu38Ac_6XnoC-j7QIoBErqbTNVQI-oeIXgf9ZMERtpDfNeWIhrGrZOHY4GjAejj8Kb6Lajge52B7MUbO9Gh6ADQmd1ZkAvFx9sPJAQjn0dQS_pCkCxVstoUvWRA-n6xY4o0148sfVD-GI0Mct_TZhMNF7lxk8gICnn83vY2fu-lPiU5lXFy59svq1cWicuNBjk7UlNfPTHmDahjdFcg8CYahUeorrDi9ZttadcmKu7XHM0npjgOtMB4lR8XwzlYbGvIt_V-7MA1lRpY2JR6GvShpoKwxBV-6qS9bagnX_OFgQHi98b62-UzHnnJbByFD3eDqaY2xPT1cBrg'; // Set your token here: "Bearer your_token_here"

  DioClient() : _dio = Dio() {
    _dio.options.baseUrl = EnvConfig.apiBaseUrl;
    _dio.options.connectTimeout = Duration(
      seconds: int.parse(EnvConfig.apiTimeout),
    );
    _dio.options.receiveTimeout = Duration(
      seconds: int.parse(EnvConfig.apiTimeout),
    );
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add hardcoded token if provided
    if (_hardcodedToken != null) {
      _dio.options.headers['Authorization'] = _hardcodedToken;
    }
  }

  void setTokenManager(TokenService tokenService) {
    _dio.interceptors.add(AuthInterceptor(tokenService));
  }

  Dio get dioInstance => _dio;

  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Add more methods for PUT, DELETE, etc. as needed
  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }
}
