import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:imed_fe/core/config/env_config.dart';
import 'package:imed_fe/core/security/auth_interceptor.dart';
import 'package:imed_fe/core/security/services/token_service.dart';

/// Comprehensive logging interceptor for debugging HTTP requests and responses
class DioLoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    print('\n🚀 ===== HTTP REQUEST START =====');
    print('🌐 URL: ${options.baseUrl}${options.path}');
    print('📋 Method: ${options.method}');

    // Log authentication token
    final authHeader = options.headers['Authorization'];
    if (authHeader != null) {
      if (authHeader.toString().startsWith('Bearer ')) {
        final token = authHeader.toString().substring(7);
        final truncatedToken =
            token.length > 50 ? '${token.substring(0, 50)}...' : token;
        print('🔑 Token: Bearer $truncatedToken');
      } else {
        print('🔑 Auth Header: $authHeader');
      }
    } else {
      print('🔑 Token: None');
    }

    // Log headers
    if (options.headers.isNotEmpty) {
      print('📤 Headers:');
      options.headers.forEach((key, value) {
        if (key.toLowerCase() != 'authorization') {
          print('   $key: $value');
        }
      });
    }

    // Log query parameters
    if (options.queryParameters.isNotEmpty) {
      print('🔍 Query Parameters:');
      options.queryParameters.forEach((key, value) {
        print('   $key: $value');
      });
    }

    // Log request body
    if (options.data != null) {
      print('📦 Request Body:');
      try {
        if (options.data is Map || options.data is List) {
          final prettyJson = const JsonEncoder.withIndent(
            '  ',
          ).convert(options.data);
          print(prettyJson);
        } else {
          print('   ${options.data}');
        }
      } catch (e) {
        print('   ${options.data} (Could not format as JSON)');
      }
    }

    print('🚀 ===== HTTP REQUEST END =====\n');
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    print('\n✅ ===== HTTP RESPONSE START =====');
    print(
      '🌐 URL: ${response.requestOptions.baseUrl}${response.requestOptions.path}',
    );
    print('📋 Method: ${response.requestOptions.method}');
    print('📊 Status Code: ${response.statusCode}');
    print('📈 Status Message: ${response.statusMessage}');

    // Log response headers
    if (response.headers.map.isNotEmpty) {
      print('📥 Response Headers:');
      response.headers.map.forEach((key, value) {
        print('   $key: ${value.join(', ')}');
      });
    }

    // Log response data
    if (response.data != null) {
      print('📦 Response Data:');
      try {
        if (response.data is Map || response.data is List) {
          final prettyJson = const JsonEncoder.withIndent(
            '  ',
          ).convert(response.data);
          print(prettyJson);
        } else {
          print('   ${response.data}');
        }
      } catch (e) {
        print('   ${response.data} (Could not format as JSON)');
      }
    }

    print('✅ ===== HTTP RESPONSE END =====\n');
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    print('\n❌ ===== HTTP ERROR START =====');
    print('🌐 URL: ${err.requestOptions.baseUrl}${err.requestOptions.path}');
    print('📋 Method: ${err.requestOptions.method}');
    print('💥 Error Type: ${err.type}');
    print('📊 Status Code: ${err.response?.statusCode ?? 'N/A'}');
    print('📈 Status Message: ${err.response?.statusMessage ?? 'N/A'}');
    print('🔥 Error Message: ${err.message}');

    // Log error response data if available
    if (err.response?.data != null) {
      print('📦 Error Response Data:');
      try {
        if (err.response!.data is Map || err.response!.data is List) {
          final prettyJson = const JsonEncoder.withIndent(
            '  ',
          ).convert(err.response!.data);
          print(prettyJson);
        } else {
          print('   ${err.response!.data}');
        }
      } catch (e) {
        print('   ${err.response!.data} (Could not format as JSON)');
      }
    }

    print('❌ ===== HTTP ERROR END =====\n');
    handler.next(err);
  }
}

class DioClient {
  final Dio _dio;

  // Hardcoded token option as requested
  static const String? _hardcodedToken =
      'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Im1VRzZ2WW5SbG9mdUx4Y2lacnU3U3B0ZjZOTSJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.geahFGDgu38Ac_6XnoC-j7QIoBErqbTNVQI-oeIXgf9ZMERtpDfNeWIhrGrZOHY4GjAejj8Kb6Lajge52B7MUbO9Gh6ADQmd1ZkAvFx9sPJAQjn0dQS_pCkCxVstoUvWRA-n6xY4o0148sfVD-GI0Mct_TZhMNF7lxk8gICnn83vY2fu-lPiU5lXFy59svq1cWicuNBjk7UlNfPTHmDahjdFcg8CYahUeorrDi9ZttadcmKu7XHM0npjgOtMB4lR8XwzlYbGvIt_V-7MA1lRpY2JR6GvShpoKwxBV-6qS9bagnX_OFgQHi98b62-UzHnnJbByFD3eDqaY2xPT1cBrg'; // Set your token here: "Bearer your_token_here"

  DioClient() : _dio = Dio() {
    // Configure base options
    _dio.options.baseUrl = EnvConfig.apiBaseUrl;
    _dio.options.connectTimeout = Duration(
      seconds: int.parse(EnvConfig.apiTimeout),
    );
    _dio.options.receiveTimeout = Duration(
      seconds: int.parse(EnvConfig.apiTimeout),
    );
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add hardcoded token if provided
    if (_hardcodedToken != null) {
      _dio.options.headers['Authorization'] = _hardcodedToken;
    }

    // Add comprehensive logging interceptor
    _dio.interceptors.add(DioLoggingInterceptor());

    // Log configuration details
    print('\n⚙️ ===== DIO CLIENT CONFIGURATION =====');
    print('🌐 Base URL: ${EnvConfig.apiBaseUrl}');
    print('⏱️ Connect Timeout: ${EnvConfig.apiTimeout}s');
    print('⏱️ Receive Timeout: ${EnvConfig.apiTimeout}s');
    print('🔧 Environment: ${EnvConfig.environment}');
    print('📤 Default Headers:');
    _dio.options.headers.forEach((key, value) {
      if (key.toLowerCase() == 'authorization') {
        if (value.toString().startsWith('Bearer ')) {
          final token = value.toString().substring(7);
          final truncatedToken =
              token.length > 50 ? '${token.substring(0, 50)}...' : token;
          print('   $key: Bearer $truncatedToken');
        } else {
          print('   $key: $value');
        }
      } else {
        print('   $key: $value');
      }
    });
    print('🔌 Interceptors: ${_dio.interceptors.length} registered');
    print('⚙️ ===== DIO CLIENT CONFIGURATION END =====\n');
  }

  void setTokenManager(TokenService tokenService) {
    _dio.interceptors.add(AuthInterceptor(tokenService));
    print('🔐 AuthInterceptor added to Dio client');
    print('🔌 Total interceptors: ${_dio.interceptors.length}');
  }

  Dio get dioInstance => _dio;

  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    print('🔄 DioClient.get() called for path: $path');
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      print('✅ DioClient.get() completed successfully for path: $path');
      return response;
    } catch (e) {
      print('❌ DioClient.get() failed for path: $path - Error: $e');
      rethrow;
    }
  }

  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    print('🔄 DioClient.post() called for path: $path');
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      print('📦 Response Data: ${response.data}');
      print('✅ DioClient.post() completed successfully for path: $path');
      return response;
    } catch (e) {
      print('❌ DioClient.post() failed for path: $path - Error: $e');
      rethrow;
    }
  }

  // Add more methods for PUT, DELETE, etc. as needed
  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    print('🔄 DioClient.put() called for path: $path');
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      print('✅ DioClient.put() completed successfully for path: $path');
      return response;
    } catch (e) {
      print('❌ DioClient.put() failed for path: $path - Error: $e');
      rethrow;
    }
  }

  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    print('🔄 DioClient.delete() called for path: $path');
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      print('✅ DioClient.delete() completed successfully for path: $path');
      return response;
    } catch (e) {
      print('❌ DioClient.delete() failed for path: $path - Error: $e');
      rethrow;
    }
  }
}
