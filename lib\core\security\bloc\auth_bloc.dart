import 'dart:developer' as developer;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:imed_fe/core/security/bloc/auth_event.dart';
import 'package:imed_fe/core/security/bloc/auth_state.dart';
import 'package:imed_fe/core/security/services/token_service.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final TokenService _tokenService;

  AuthBloc(this._tokenService) : super(const AuthInitial()) {
    on<AuthLoginEvent>(_onLogin);
    on<AuthCheckStatusEvent>(_onCheckStatus);
    on<AuthRefreshTokenEvent>(_onRefreshToken);
    on<AuthLogoutEvent>(_onLogout);
    on<AuthPrintInfoEvent>(_onPrintInfo);
  }

  Future<void> _onLogin(AuthLoginEvent event, Emitter<AuthState> emit) async {
    try {
      emit(const AuthLoading());
      await _tokenService.saveTokens(event.tokenModel);
      developer.log('✅ Login successful', name: 'AuthBloc', level: 800);
      emit(
        AuthAuthenticated(
          accessToken: event.tokenModel.accessToken,
          expiresAt: event.tokenModel.expiresAt,
        ),
      );
    } catch (e) {
      emit(AuthError('Login failed: $e'));
    }
  }

  Future<void> _onCheckStatus(
    AuthCheckStatusEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      final isAuth = await _tokenService.isAuthenticated();
      if (isAuth) {
        final token = await _tokenService.getAccessToken();
        developer.log('✅ User authenticated', name: 'AuthBloc', level: 800);
        emit(AuthAuthenticated(accessToken: token ?? ''));
      } else {
        developer.log('❌ User not authenticated', name: 'AuthBloc', level: 900);
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError('Status check failed: $e'));
    }
  }

  Future<void> _onRefreshToken(
    AuthRefreshTokenEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());
      final success = await _tokenService.validateAndRefreshToken();
      if (success) {
        final token = await _tokenService.getAccessToken();
        developer.log('✅ Token refreshed', name: 'AuthBloc', level: 800);
        emit(AuthAuthenticated(accessToken: token ?? ''));
      } else {
        emit(const AuthUnauthenticated(message: 'Session expired'));
      }
    } catch (e) {
      emit(AuthError('Refresh failed: $e'));
    }
  }

  Future<void> _onLogout(AuthLogoutEvent event, Emitter<AuthState> emit) async {
    try {
      await _tokenService.logout();
      developer.log('✅ Logged out', name: 'AuthBloc', level: 800);
      emit(const AuthUnauthenticated(message: 'Logged out'));
    } catch (e) {
      emit(AuthError('Logout failed: $e'));
    }
  }

  Future<void> _onPrintInfo(
    AuthPrintInfoEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      await _tokenService.printTokenInfo();
    } catch (e) {
      developer.log('❌ Error printing info: $e', name: 'AuthBloc', level: 1000);
    }
  }
}
