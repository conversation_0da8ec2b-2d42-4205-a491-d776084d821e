import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/utils/file_picker_utils.dart';
import '../../../../../core/widgets/custom_elevated_button.dart';
import '../../../../../core/widgets/reusable_icon_button.dart';
import '../../data/repositories/appointment_repository_impl.dart';
import '../../domain/entities/new_appointment_request.dart';
import '../../domain/usecases/cancel_appointment_usecase.dart';
import '../../domain/usecases/confirm_appointment_usecase.dart';
import '../../domain/usecases/get_appointment_detail_usecase.dart';
import '../../domain/usecases/get_appointment_usecase.dart';
import '../../domain/usecases/new_appointment_usecase.dart';
import '../bloc/appointment_bloc.dart';

class NewAppointmentPage extends StatelessWidget {
  const NewAppointmentPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AppointmentBloc(
        getAppointmentsUseCase: GetAppointmentsUseCase(AppointmentRepositoryImpl()),
        cancelAppointmentUseCase: CancelAppointmentUseCase(AppointmentRepositoryImpl()),
        confirmAppointmentUseCase: ConfirmAppointmentUseCase(AppointmentRepositoryImpl()),
        getAppointmentDetailUsecase: GetAppointmentDetailUsecase(AppointmentRepositoryImpl()),
        newAppointmentUsecase: NewAppointmentUsecase(AppointmentRepositoryImpl()),
      ),
      child: const _NewAppointmentView(),
    );
  }
}

class _NewAppointmentView extends StatefulWidget {
  const _NewAppointmentView();

  @override
  State<_NewAppointmentView> createState() => _NewAppointmentPageState();
}

class _NewAppointmentPageState extends State<_NewAppointmentView> {
  final _formKey = GlobalKey<FormState>();

  DateTime? selectedDate = DateTime(1990, 2, 19);
  TimeOfDay? selectedTime;
  String? selectedBloodGroup = 'AB+';
  String? selectedGender = 'Male';

  final TextEditingController _firstNameController = TextEditingController(text: 'MD');
  final TextEditingController _lastNameController = TextEditingController(text: 'Nasir');
  final TextEditingController _emailController = TextEditingController(text: 'yourgmail.com');
  final TextEditingController _countryCodeController = TextEditingController(text: '+123');
  final TextEditingController _phoneController = TextEditingController(text: '23456789');
  final TextEditingController _problemDescController = TextEditingController(text: 'With a passion for healing and over 15');
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();
  final TextEditingController _uploadController = TextEditingController();

  final List<String> _complaintsList = [
    'Neck Injury',
    'Back Pain',
    'Headache',
    'Fever',
    'Cough',
    'Chest Pain',
    'Stomach Pain',
    'Joint Pain',
    'Allergy',
    'Other'
  ];

  List<String?> _selectedComplaints = [null];

  List<PlatformFile> _uploadedFiles = [];

  PlatformFile? _selectedProfilePhoto;

  @override
  void initState() {
    super.initState();
    _dateController.text = selectedDate != null ? DateFormat('dd/MM/yyyy').format(selectedDate!) : '';
    _timeController.text = '11:00 AM - 11:30 AM';
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _countryCodeController.dispose();
    _phoneController.dispose();
    _problemDescController.dispose();
    _dateController.dispose();
    _timeController.dispose();
    _uploadController.dispose();
    super.dispose();
  }

  void _setAppointment() {
    if (_formKey.currentState!.validate()) {
      final validComplaints = _selectedComplaints.where((complaint) => complaint != null).toList();

      if (validComplaints.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select at least one complaint'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final request = NewAppointmentRequest(
        firstName: _firstNameController.text,
        lastName: _lastNameController.text,
        email: _emailController.text,
        countryCode: _countryCodeController.text,
        phone: _phoneController.text,
        bloodGroup: selectedBloodGroup!,
        gender: selectedGender!,
        date: _dateController.text,
        timeSlot: _timeController.text,
        complaints: validComplaints.join(', '),
        problemDescription: _problemDescController.text,
      );

      context.read<AppointmentBloc>().add(CreateNewAppointmentEvent(request));
    }
  }

  Widget _spacer({double height = AppConstants.paddingMedium}) {
    return SizedBox(height: height);
  }

  Widget _buildTwoFieldsRow(Widget field1, Widget field2, bool isWide) {
    if (isWide) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: field1),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(child: field2),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: double.infinity, child: field1),
          _spacer(),
          SizedBox(width: double.infinity, child: field2),
        ],
      );
    }
  }

  void _addMoreComplaints() {
    setState(() {
      _selectedComplaints.add(null);
    });
  }

  void _removeComplaint(int index) {
    setState(() {
      if (_selectedComplaints.length > 1) {
        _selectedComplaints.removeAt(index);
      }
    });
  }

  Future<void> _uploadFiles() async {
    final files = await FilePickerUtils.pickMultipleFiles(
      allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
      type: FileType.custom,
    );

    if (files != null && files.isNotEmpty) {
      setState(() {
        _uploadedFiles.addAll(files);
        _uploadController.text = '${_uploadedFiles.length} file(s) selected';
      });
    }
  }

  Future<void> _uploadProfilePhoto() async {
    final file = await FilePickerUtils.pickSingleFile(
      type: FileType.custom,
      allowedExtensions: ['jpg', 'jpeg', 'png'],
    );

    if (file != null) {
      setState(() {
        _selectedProfilePhoto = file;
      });
    }
  }

  void _handleBackButton() {
    context.go('/appointments');
  }

  List<String> _getAvailableComplaints(int currentIndex) {
    final selectedComplaints = _selectedComplaints
        .where((complaint) => complaint != null)
        .where((complaint) => complaint != _selectedComplaints[currentIndex])
        .toList();

    return _complaintsList.where((complaint) => !selectedComplaints.contains(complaint)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocListener<AppointmentBloc, AppointmentState>(
        listener: (context, state) {
          if (state is NewAppointmentSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Appointment created successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            Future.delayed(const Duration(milliseconds: 1500), () {
              context.go('/appointments');
            });
          } else if (state is NewAppointmentFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error: ${state.error}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: BlocBuilder<AppointmentBloc, AppointmentState>(
          builder: (context, state) {
            final bool isLoading = state is NewAppointmentLoading;

            return SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingExtraLarge,
                vertical: AppConstants.paddingLarge,
              ),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 1100),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: ReusableIconButton(
                              icon: Icons.arrow_back_ios_new,
                              onPressed: _handleBackButton,
                              color: AppConstants.textPrimaryColor,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: AppConstants.paddingMedium),
                          const Text(
                            "New Appointment",
                            style: TextStyle(
                              fontSize: AppConstants.fontSize22,
                              fontWeight: FontWeight.w600,
                              color: AppConstants.textPrimaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppConstants.sizedBoxHeightLarge),

                      Form(
                        key: _formKey,
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            const double wideBreakpoint = 800;
                            bool isWide = constraints.maxWidth > wideBreakpoint;

                            return Flex(
                              direction: Axis.horizontal,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: isWide ? 2 : 1,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      _buildTwoFieldsRow(
                                        _buildCustomTextField("First Name *", "MD", _firstNameController, isRequired: true),
                                        _buildCustomTextField("Last Name", "Nasir", _lastNameController),
                                        isWide,
                                      ),
                                      _spacer(height: AppConstants.paddingLarge),

                                      _buildTwoFieldsRow(
                                        _buildCustomTextField("Patient Email", "yourgmail.com", _emailController),
                                        _buildPhoneNumberField(),
                                        isWide,
                                      ),
                                      _spacer(height: AppConstants.paddingLarge),

                                      _buildTwoFieldsRow(
                                        _buildDropdownField("Blood Group", ["AB+", "A+", "A-", "B+", "B-", "AB-", "O+", "O-"], selectedBloodGroup, (String? newValue) {
                                          setState(() {
                                            selectedBloodGroup = newValue;
                                          });
                                        }, isRequired: true),
                                        _buildDropdownField("Gender *", ["Male", "Female"], selectedGender, (String? newValue) {
                                          setState(() {
                                            selectedGender = newValue;
                                          });
                                        }, isRequired: true),
                                        isWide,
                                      ),
                                      _spacer(height: AppConstants.paddingLarge),
                                      _buildTwoFieldsRow(
                                        _buildDateField(),
                                        _buildTimeField(),
                                        isWide,
                                      ),
                                      _spacer(height: AppConstants.paddingLarge),

                                      _buildTwoFieldsRow(
                                        _buildUploadField(),
                                        const SizedBox.shrink(),
                                        isWide,
                                      ),
                                      _spacer(height: AppConstants.paddingExtraLarge),

                                      _buildComplaintsSection(),
                                      _spacer(height: AppConstants.paddingMedium),

                                      _buildCustomTextField("Problem Description", "With a passion for healing and over 15", _problemDescController, maxLines: 3),
                                    ],
                                  ),
                                ),

                                if (isWide)
                                  const SizedBox(width: AppConstants.paddingExtraLarge * 2),

                                if (isWide)
                                  Expanded(
                                    flex: 1,
                                    child: _buildUploadPhotoWidget(),
                                  ),
                              ],
                            );
                          },
                        ),
                      ),

                      LayoutBuilder(
                        builder: (context, constraints) {
                          const double wideBreakpoint = 800;
                          bool isWide = constraints.maxWidth > wideBreakpoint;

                          if (!isWide) {
                            return Column(
                              children: [
                                const SizedBox(height: AppConstants.sizedBoxHeightLarge),
                                _buildUploadPhotoWidget(),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),

                      const SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),

                      Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                            boxShadow: [
                              BoxShadow(
                                color: AppConstants.primaryColor.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: CustomElevatedButton(
                            onPressed: isLoading ? null : _setAppointment,
                            text: "Set Appointment",
                            backgroundColor: AppConstants.primaryColor,
                            foregroundColor: Colors.white,
                            borderRadius: AppConstants.borderRadiusSmall,
                            fontSize: AppConstants.fontSizeMedium,
                            fontWeight: FontWeight.w600,
                            prefixIcon: isLoading
                                ? const SizedBox(
                              height: 24,
                              width: 24,
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                strokeWidth: 3,
                              ),
                            )
                                : null,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildComplaintsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Complaints *",
          style: TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),

        Column(
          children: List.generate(_selectedComplaints.length, (index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
              child: Row(
                children: [
                  Expanded(
                    child: _buildComplaintDropdown(index),
                  ),
                  if (_selectedComplaints.length > 1)
                    ReusableIconButton(
                      icon: Icons.remove_circle_outline,
                      onPressed: () => _removeComplaint(index),
                      color: Colors.red,
                    ),
                ],
              ),
            );
          }),
        ),

        _buildAddMoreButton(),
      ],
    );
  }

  Widget _buildComplaintDropdown(int index) {
    final availableComplaints = _getAvailableComplaints(index);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(color: AppConstants.borderColor),
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedComplaints[index],
        decoration: const InputDecoration(
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          border: InputBorder.none,
          hintText: "Select Complaint",
        ),
        validator: (value) {
          if (value == null && index == 0) {
            return 'Please select at least one complaint';
          }
          return null;
        },
        items: [
          const DropdownMenuItem<String>(
            value: null,
            child: Text(
              "Select Complaint",
              style: TextStyle(
                color: AppConstants.textMidColor,
                fontSize: AppConstants.fontSize14,
              ),
            ),
          ),
          ...availableComplaints.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: const TextStyle(
                  color: AppConstants.textHighColor,
                  fontSize: AppConstants.fontSize14,
                ),
              ),
            );
          }).toList(),
        ],
        onChanged: (String? newValue) {
          setState(() {
            _selectedComplaints[index] = newValue;
          });
        },
        icon: const Icon(Icons.arrow_drop_down, color: AppConstants.textMidColor),
      ),
    );
  }

  Widget _buildUploadPhotoWidget() {
    return Column(
      children: [
        GestureDetector(
          onTap: _uploadProfilePhoto,
          child: Container(
            height: 140,
            width: 140,
            decoration: BoxDecoration(
              color: AppConstants.inputFieldBackgroundColor,
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              border: Border.all(
                color: AppConstants.borderColor,
                width: 1.5,
              ),
            ),
            child: _selectedProfilePhoto != null
                ? ClipRRect(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              child: kIsWeb
                  ? _buildWebImage()
                  : Image.file(
                File(_selectedProfilePhoto!.path!),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildUploadPhotoPlaceholder();
                },
              ),
            )
                : _buildUploadPhotoPlaceholder(),
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        Text(
          _selectedProfilePhoto != null ? _selectedProfilePhoto!.name : "Upload Photo",
          style: TextStyle(
            color: AppConstants.textMidColor,
            fontSize: AppConstants.fontSizeSmall,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildWebImage() {
    if (_selectedProfilePhoto == null) return _buildUploadPhotoPlaceholder();

    if (_selectedProfilePhoto!.bytes != null) {
      return Image.memory(
        _selectedProfilePhoto!.bytes!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildUploadPhotoPlaceholder();
        },
      );
    } else {
      return _buildUploadPhotoPlaceholder();
    }
  }

  Widget _buildUploadPhotoPlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.camera_alt_outlined,
          size: 40,
          color: AppConstants.textMidColor.withOpacity(0.7),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          "Upload Photo",
          style: TextStyle(
            color: AppConstants.textMidColor,
            fontSize: AppConstants.fontSizeSmall,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildCustomTextField(String label, String hint, TextEditingController controller, {int maxLines = 1, bool isRequired = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        CustomTextField(
          controller: controller,
          labelText: '',
          hintText: hint,
          validator: (value) {
            if (isRequired && (value == null || value.isEmpty)) {
              return 'This field is required';
            }
            return null;
          },
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
            hintText: hint,
            hintStyle: TextStyle(
              color: AppConstants.textMidColor,
              fontSize: AppConstants.fontSize14,
            ),
            border: InputBorder.none,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.primaryColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.errorColor),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.errorColor),
            ),
          ),
          style: const TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
          ),
          maxLines: maxLines,
        ),
      ],
    );
  }

  Widget _buildPhoneNumberField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Phone Number *",
          style: TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
            border: Border.all(color: AppConstants.borderColor),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: CustomTextField(
                  controller: _countryCodeController,
                  labelText: '',
                  hintText: '+123',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '*';
                    }
                    return null;
                  },
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                    border: InputBorder.none,
                    hintText: '+123',
                    hintStyle: TextStyle(
                      color: AppConstants.textMidColor,
                      fontSize: AppConstants.fontSize14,
                    ),
                  ),
                  style: const TextStyle(
                    color: AppConstants.textHighColor,
                    fontSize: AppConstants.fontSize14,
                  ),
                ),
              ),

              Container(
                width: 1,
                height: 24,
                color: AppConstants.borderColor,
              ),

              Expanded(
                child: CustomTextField(
                  controller: _phoneController,
                  labelText: '',
                  hintText: '23456789',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                    border: InputBorder.none,
                    hintText: '23456789',
                    hintStyle: TextStyle(
                      color: AppConstants.textMidColor,
                      fontSize: AppConstants.fontSize14,
                    ),
                  ),
                  style: const TextStyle(
                    color: AppConstants.textHighColor,
                    fontSize: AppConstants.fontSize14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField(String label, List<String> options, String? selectedValue, ValueChanged<String?> onChanged, {bool isRequired = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
            border: Border.all(color: AppConstants.borderColor),
          ),
          child: DropdownButtonFormField<String>(
            value: selectedValue,
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              border: InputBorder.none,
            ),
            validator: (value) {
              if (isRequired && value == null) {
                return 'Please select a value';
              }
              return null;
            },
            items: options.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(
                  value,
                  style: const TextStyle(
                    color: AppConstants.textHighColor,
                    fontSize: AppConstants.fontSize14,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            icon: const Icon(Icons.arrow_drop_down, color: AppConstants.textMidColor),
          ),
        ),
      ],
    );
  }

  Widget _buildDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Appointment Date *",
          style: TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        CustomTextField(
          controller: _dateController,
          labelText: '',
          hintText: "DD/MM/YYYY",
          readOnly: true,
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: selectedDate ?? DateTime.now(),
              firstDate: DateTime(1970),
              lastDate: DateTime(2100),
            );
            if (date != null) {
              setState(() {
                selectedDate = date;
                _dateController.text = DateFormat('dd/MM/yyyy').format(date);
              });
            }
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Date is required';
            }
            return null;
          },
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
            hintText: "DD/MM/YYYY",
            hintStyle: TextStyle(
              color: AppConstants.textMidColor,
              fontSize: AppConstants.fontSize14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.borderColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.primaryColor),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                Icons.calendar_today_outlined,
                color: AppConstants.textMidColor,
                size: 20,
              ),
              onPressed: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: selectedDate ?? DateTime.now(),
                  firstDate: DateTime(1970),
                  lastDate: DateTime(2100),
                );
                if (date != null) {
                  setState(() {
                    selectedDate = date;
                    _dateController.text = DateFormat('dd/MM/yyyy').format(date);
                  });
                }
              },
            ),
          ),
          style: const TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Appointment Time *",
          style: TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        CustomTextField(
          controller: _timeController,
          labelText: '',
          hintText: "HH:MM AM/PM - HH:MM AM/PM",
          readOnly: true,
          onTap: () async {
            final time = await showTimePicker(
              context: context,
              initialTime: selectedTime ?? TimeOfDay.now(),
            );
            if (time != null) {
              setState(() {
                selectedTime = time;
                final endHour = time.hour + (time.minute + 30) ~/ 60;
                final endMinute = (time.minute + 30) % 60;
                final endTime = TimeOfDay(hour: endHour, minute: endMinute);

                _timeController.text = '${time.format(context)} - ${endTime.format(context)}';
              });
            }
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Time is required';
            }
            return null;
          },
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
            hintText: "HH:MM AM/PM - HH:MM AM/PM",
            hintStyle: TextStyle(
              color: AppConstants.textMidColor,
              fontSize: AppConstants.fontSize14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.borderColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.primaryColor),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                Icons.access_time,
                color: AppConstants.textMidColor,
                size: 20,
              ),
              onPressed: () async {
                final time = await showTimePicker(
                  context: context,
                  initialTime: selectedTime ?? TimeOfDay.now(),
                );
                if (time != null) {
                  setState(() {
                    selectedTime = time;
                    // Simple time slot calculation (e.g., 30 minutes)
                    final endHour = time.hour + (time.minute + 30) ~/ 60;
                    final endMinute = (time.minute + 30) % 60;
                    final endTime = TimeOfDay(hour: endHour, minute: endMinute);

                    _timeController.text = '${time.format(context)} - ${endTime.format(context)}';
                  });
                }
              },
            ),
          ),
          style: const TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
          ),
        ),
      ],
    );
  }

  Widget _buildUploadField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Upload Reports",
          style: TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        CustomTextField(
          controller: _uploadController,
          labelText: '',
          hintText: "Upload Reports",
          readOnly: true,
          onTap: _uploadFiles,
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
            hintText: "Upload Reports",
            hintStyle: TextStyle(
              color: AppConstants.textMidColor,
              fontSize: AppConstants.fontSize14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.borderColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              borderSide: BorderSide(color: AppConstants.primaryColor),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                Icons.upload_file_outlined,
                color: AppConstants.textMidColor,
                size: 20,
              ),
              onPressed: _uploadFiles,
            ),
          ),
          style: const TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSize14,
          ),
        ),
        // Show selected files
        if (_uploadedFiles.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Selected files: ${_uploadedFiles.length}',
            style: TextStyle(
              color: AppConstants.textMidColor,
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAddMoreButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(color: AppConstants.primaryColor),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _addMoreComplaints,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingSmall,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.add,
                  color: AppConstants.primaryColor,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  "Add More",
                  style: TextStyle(
                    color: AppConstants.primaryColor,
                    fontSize: AppConstants.fontSizeMedium,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// CustomTextField widget (keep this as it's used in the file)
class CustomTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String labelText;
  final String? hintText;
  final bool obscureText;
  final TextInputType keyboardType;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final bool readOnly;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final InputDecoration? decoration;
  final TextStyle? style;
  final int? maxLines;

  const CustomTextField({
    super.key,
    this.controller,
    this.labelText = '',
    this.hintText,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.suffixIcon,
    this.prefixIcon,
    this.validator,
    this.inputFormatters,
    this.readOnly = false,
    this.onTap,
    this.onChanged,
    this.decoration,
    this.style,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      inputFormatters: inputFormatters,
      readOnly: readOnly,
      onTap: onTap,
      onChanged: onChanged,
      maxLines: maxLines,
      decoration: decoration ?? InputDecoration(
        labelText: labelText,
        hintText: hintText,
        suffixIcon: suffixIcon,
        prefixIcon: prefixIcon,
      ),
      style: style,
    );
  }
}