import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ReusableSvgImage extends StatelessWidget {
  final String assetPath;
  final double? width;
  final double? height;
  final Color? color;
  final Widget? placeholder;

  const ReusableSvgImage({
    super.key,
    required this.assetPath,
    this.width,
    this.height,
    this.color,
    this.placeholder,
  });

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      assetPath,
      width: width,
      height: height,
      colorFilter: color != null ? ColorFilter.mode(color!, BlendMode.srcIn) : null,
      placeholderBuilder: placeholder != null ? (BuildContext context) => placeholder! : null,
    );
  }
}