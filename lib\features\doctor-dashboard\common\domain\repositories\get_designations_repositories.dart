import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_designation/get_designation_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_designation/get_designation_response_model.dart';

abstract class GetDesignationsRepositories {
  Future<Either<Failure, GetDesignationsResponseModel>> getAllDesignations(
    GetDesignationsRequestModel requestModel,
  );
}
