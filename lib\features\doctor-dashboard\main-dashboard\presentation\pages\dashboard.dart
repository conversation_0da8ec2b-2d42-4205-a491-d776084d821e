import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/di/injection_container.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/widgets/searchbar.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_request.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/dashboard/dashboard_bloc.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/dashboard/dashboard_state.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/dashboard/dashboard_event.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/doctor_highlights/doctor_highlights_bloc.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/doctor_highlights/doctor_highlights_state.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/bloc/doctor_highlights/doctor_highlights_event.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/widgets/weekly_calendar.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/widgets/choice-widget.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<DashboardBloc>(
          create:
              (_) => DashboardBloc(
                getAppointmentbyDoctorRepositories: sl(),
                getAppointmentDetailsByIDRepositories: sl(),
                updateAppointmentStatusByAppointmentidRepositories: sl(),
              ),
        ),
        BlocProvider<DoctorHighlightsBloc>(
          create:
              (_) => DoctorHighlightsBloc(
                getDoctorAppointmentStatisticsRepositories: sl(),
              ),
        ),
      ],
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(color: Color(0xFFECECEC)),
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: AppConstants.sizedBoxHeightMedium,
            children: const [
              SizedBox(
                width: 506,
                height: 40,
                child: CustomSearchBar(
                  hintText: "Search",
                  backgroundColor: Color.fromRGBO(34, 96, 255, 0.6),
                  hintColor: Colors.white,
                  iconColor: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
              ),
              _DashboardGreetingComponent(),
              SizedBox(height: AppConstants.sizedBoxHeightSmall),
              _DashboardSummaryCardsComponent(),
              Wrap(
                spacing: AppConstants.sizedBoxHeightMedium,
                runSpacing: AppConstants.sizedBoxHeightMedium,
                children: [
                  _AppointmentRequestComponent(),
                  _TodayAppointmentsComponent(),
                  _DashboardQueryComponent(),
                  _RecentPatientsComponent(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DashboardGreetingComponent extends StatelessWidget {
  const _DashboardGreetingComponent();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome, Dr. Robert',
          style: TextStyle(
            fontSize: AppConstants.fontSizeExtraLarge,
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: AppConstants.sizedBoxHeightSmall),
        Text(
          'Dentist',
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            color: AppConstants.textHighColor,
          ),
        ),
      ],
    );
  }
}

class _DashboardSummaryCardsComponent extends StatelessWidget {
  const _DashboardSummaryCardsComponent();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DoctorHighlightsBloc, DoctorHighlightsState>(
      builder: (context, state) {
        if (state is DoctorHighlightsLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is DoctorHighlightsLoaded) {
          final totalAppointments = state.totalAppointments;
          final totalPatients = state.totalPatients;
          final totalClinicConsulting = state.totalClinicConsulting;
          final totalVideoConsulting = state.totalVideoConsulting;

          return Wrap(
            spacing: AppConstants.sizedBoxHeightMedium,
            runSpacing: AppConstants.sizedBoxHeightMedium,
            children: [
              LayoutBuilder(
                builder: (context, constraints) {
                  const int cardCount = 4;
                  const double minCardWidth = 260;
                  const double spacing = AppConstants.sizedBoxHeightLarge;

                  int cardsPerRow =
                      (constraints.maxWidth + spacing) ~/
                      (minCardWidth + spacing);
                  cardsPerRow = cardsPerRow.clamp(1, cardCount);

                  double cardWidth =
                      (constraints.maxWidth - (cardsPerRow - 1) * spacing) /
                      cardsPerRow;

                  return Wrap(
                    spacing: spacing,
                    runSpacing: AppConstants.sizedBoxHeightMedium,
                    children: [
                      LabelledInfoCard(
                        maxWidth: cardWidth.clamp(
                          minCardWidth,
                          double.infinity,
                        ),
                        title: 'Appointments',
                        value: totalAppointments.toString(),
                        iconPath: "assets/icons/doctor/calendar-icon.svg",
                        backgroundColor: const Color(0xFF7A6EFE),
                      ),
                      LabelledInfoCard(
                        maxWidth: cardWidth.clamp(
                          minCardWidth,
                          double.infinity,
                        ),
                        title: 'Total Patients',
                        value: totalPatients.toString(),
                        iconPath: "assets/icons/doctor/people-icon.svg",
                        backgroundColor: const Color(0xFFFF5363),
                      ),
                      LabelledInfoCard(
                        maxWidth: cardWidth.clamp(
                          minCardWidth,
                          double.infinity,
                        ),
                        title: 'Clinic Consulting',
                        value: totalClinicConsulting.toString(),
                        iconPath: "assets/icons/doctor/hospital-icon.svg",
                        backgroundColor: const Color(0xFFFFA901),
                      ),
                      LabelledInfoCard(
                        maxWidth: cardWidth.clamp(
                          minCardWidth,
                          double.infinity,
                        ),
                        title: 'Video Consulting',
                        value: totalVideoConsulting.toString(),
                        iconPath: "assets/icons/doctor/video-icon.svg",
                        backgroundColor: const Color(0xFF24A8FA),
                      ),
                    ],
                  );
                },
              ),
            ],
          );
        } else if (state is DoctorHighlightsError) {
          return Center(
            child: Text('Failed to load summary: ${state.message}'),
          );
        } else {
          return const Center(child: Text('Unexpected state'));
        }
      },
    );
  }
}

class _AppointmentRequestComponent extends StatefulWidget {
  const _AppointmentRequestComponent();

  @override
  State<_AppointmentRequestComponent> createState() =>
      _AppointmentRequestComponentState();
}

class _AppointmentRequestComponentState
    extends State<_AppointmentRequestComponent> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<DoctorHighlightsBloc>(
      context,
    ).add(const CallGetDoctorHighlightsData());
    BlocProvider.of<DashboardBloc>(context).add(CallGetDashboardData());
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double minWidth = 482;
        double calculatedMaxWidth =
            (MediaQuery.of(context).size.width - 308) *
            0.4; // (MediaQuery.of(context).size.width - 240 - 18 * 2 - 16 * 2) * 2 / 5;
        double maxWidth =
            calculatedMaxWidth <= constraints.minWidth
                ? minWidth
                : calculatedMaxWidth;
        return SizedBox(
          width: maxWidth.clamp(minWidth, double.infinity),
          height: 613,
          child: CardWindow(
            title: "Appointment Request",
            optionWidget: GestureDetector(
              onTap: () {},
              child: Row(
                spacing: AppConstants.sizedBoxHeightSmall,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'View All',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textMidColor,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  ReusableSvgImage(
                    assetPath: "assets/icons/doctor/next-icon.svg",
                    width: 20,
                    height: 20,
                  ),
                ],
              ),
            ),
            child: BlocBuilder<DashboardBloc, DashboardState>(
              builder: (context, state) {
                if (state is DashboardLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is DashboardLoaded) {
                  final appointmentRequestList =
                      state.appointmentRequests.appointments
                          .where(
                            (appointment) => appointment.status != 'completed',
                          )
                          .toList();

                  return ListView.separated(
                    itemCount: appointmentRequestList.length,
                    separatorBuilder:
                        (context, index) =>
                            SizedBox(height: AppConstants.sizedBoxHeightMedium),
                    physics: BouncingScrollPhysics(),
                    itemBuilder: (context, index) {
                      final appointment = appointmentRequestList[index];
                      return AppointRequestListItem(
                        isConfirmed:
                            appointment.status == 'confirmed'
                                ? true
                                : appointment.status == 'denied'
                                ? false
                                : null,
                        appointmentId: appointment.appointmentId,
                        isLoading:
                            state.updatingAppointmentId ==
                            appointment.appointmentId,
                        avatarUrl: "assets/images/person1.png",
                        patientName: appointment.patientName,
                        appointmentInfoTime: appointment.startTime,
                        onStatusChanged: (appointmentId, isConfirmed) {
                          context.read<DashboardBloc>().add(
                            CallUpdateAppointmentStatusByAppointmentId(
                              appointmentStatusByAppointmentidRequest:
                                  UpdateAppointmentStatusByAppointmentidRequest(
                                    appointmentId: appointmentId,
                                    status:
                                        isConfirmed ? 'confirmed' : 'denied',
                                  ),
                            ),
                          );
                        },
                      );
                    },
                  );
                } else if (state is DashboardError) {
                  return Center(
                    child: Text(
                      'Failed to load appointments: ${state.message}',
                    ),
                  );
                } else {
                  return const Center(child: Text('Unexpected state'));
                }
              },
            ),
          ),
        );
      },
    );
  }
}

class AppointRequestListItem extends StatefulWidget {
  final bool? isConfirmed;
  final String patientName;
  final String appointmentInfoTime;
  final String avatarUrl;
  final String appointmentId;
  final bool isLoading;
  final Function(String appointmentId, bool isConfirmed)? onStatusChanged;

  const AppointRequestListItem({
    super.key,
    this.isConfirmed,
    required this.patientName,
    required this.appointmentInfoTime,
    required this.avatarUrl,
    required this.appointmentId,
    this.isLoading = false,
    this.onStatusChanged,
  });

  @override
  State<AppointRequestListItem> createState() => _AppointRequestListItemState();
}

class _AppointRequestListItemState extends State<AppointRequestListItem> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CircleAvatar(
          foregroundImage: AssetImage(widget.avatarUrl),
          radius: 27.5,
        ),
        SizedBox(width: AppConstants.sizedBoxHeightMedium),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: AppConstants.sizedBoxHeightSmall,
          children: [
            Text(
              widget.patientName,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              widget.appointmentInfoTime,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConstants.textMidColor,
              ),
            ),
          ],
        ),
        Spacer(),
        if (widget.isLoading)
          const SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        else
          ChoiceWidget(
            isConfirmed: widget.isConfirmed,
            onConfirm:
                () => widget.onStatusChanged?.call(widget.appointmentId, true),
            onDeny:
                () => widget.onStatusChanged?.call(widget.appointmentId, false),
          ),
      ],
    );
  }
}

class _TodayAppointmentsComponent extends StatefulWidget {
  const _TodayAppointmentsComponent();

  @override
  State<_TodayAppointmentsComponent> createState() =>
      _TodayAppointmentsComponentState();
}

class _TodayAppointmentsComponentState
    extends State<_TodayAppointmentsComponent> {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double minWidth = 413;
        double calculatedMaxWidth =
            (MediaQuery.of(context).size.width - 308) *
            0.4; // (MediaQuery.of(context).size.width - 240 - 18 * 2 - 16 * 2) * 2 / 5;
        double maxWidth =
            calculatedMaxWidth <= constraints.minWidth
                ? minWidth
                : calculatedMaxWidth;
        return SizedBox(
          width: maxWidth.clamp(minWidth, double.infinity),
          height: 613,
          child: CardWindow(
            title: "Today Appointments",
            child: Column(
              children: [
                Expanded(
                  child: BlocBuilder<DashboardBloc, DashboardState>(
                    builder: (context, state) {
                      if (state is DashboardLoading) {
                        return const Center(child: CircularProgressIndicator());
                      } else if (state is DashboardLoaded) {
                        final todayAppointments = state.todayAppointments;
                        if (todayAppointments.appointments.isEmpty) {
                          return const Center(
                            child: Text('No appointments today.'),
                          );
                        }
                        return ListView.builder(
                          itemCount: todayAppointments.appointments.length,
                          physics: BouncingScrollPhysics(),
                          itemBuilder: (context, index) {
                            final appointment =
                                todayAppointments.appointments[index];
                            return TodayAppointmentList(
                              assetImage: "assets/images/person1.png",
                              patientName: appointment.patientName,
                              appointmentType:
                                  appointment.appointmentType == "virtual"
                                      ? "Video Consultation"
                                      : "Clinic Consulting",
                              appointmentStartTime: appointment.startTime,
                              appointmentEndTime: appointment.endTime,
                            );
                          },
                        );
                      } else if (state is DashboardError) {
                        return Center(
                          child: Text(
                            'Failed to load appointments: ${state.message}',
                          ),
                        );
                      } else {
                        return const Center(child: Text('Unexpected state'));
                      }
                    },
                  ),
                ),
                WeeklyCalendar(),
                SizedBox(height: AppConstants.sizedBoxHeightLarge),
                UpcomingSchedule(),
              ],
            ),
          ),
        );
      },
    );
  }
}

class TodayAppointmentList extends StatefulWidget {
  final String assetImage;
  final String patientName;
  final String appointmentType;
  final String appointmentStartTime;
  final String appointmentEndTime;
  const TodayAppointmentList({
    super.key,
    required this.assetImage,
    required this.patientName,
    required this.appointmentType,
    required this.appointmentStartTime,
    required this.appointmentEndTime,
  });

  @override
  State<TodayAppointmentList> createState() => _TodayAppointmentListState();
}

class _TodayAppointmentListState extends State<TodayAppointmentList> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color: _isHovered ? const Color(0xFFE9F6FE) : Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            CircleAvatar(
              foregroundImage: AssetImage(widget.assetImage),
              radius: 24.5,
            ),
            SizedBox(width: AppConstants.sizedBoxHeightMedium),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  widget.patientName,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color:
                        _isHovered
                            ? AppConstants.primaryColor
                            : AppConstants.textHighColor,
                  ),
                ),
                SizedBox(height: AppConstants.sizedBoxHeightSmall),
                Text(
                  widget.appointmentType,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            Spacer(),
            Text(
              (() {
                final now = DateTime.now();
                final appointmentStartDateTime = DateTime(
                  now.year,
                  now.month,
                  now.day,
                  int.parse(widget.appointmentStartTime.split(":")[0]),
                  int.parse(widget.appointmentStartTime.split(":")[1]),
                );
                final appointmentEndDateTime = DateTime(
                  now.year,
                  now.month,
                  now.day,
                  int.parse(widget.appointmentEndTime.split(":")[0]),
                  int.parse(widget.appointmentEndTime.split(":")[1]),
                );
                return appointmentStartDateTime.isAfter(now) &&
                        appointmentEndDateTime.isBefore(now)
                    ? "Ongoing"
                    : (appointmentEndDateTime.isAfter(now)
                        ? "${appointmentStartDateTime.hour.toString().padLeft(2, '0')}:${appointmentStartDateTime.minute.toString().padLeft(2, '0')}"
                        : "Completed");
              })(),
              style: TextStyle(
                color: AppConstants.textMidColor,
                fontWeight: FontWeight.w400,
                fontSize: AppConstants.fontSize14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PatientRecord extends StatelessWidget {
  const PatientRecord({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 210,
      child: CardWindow(
        title: "Patients",
        padding: EdgeInsets.only(
          top: AppConstants.paddingSmall,
          left: 0,
          right: 0,
          bottom: 0,
        ),
        backgroundColor: Colors.transparent,
        optionWidget: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: DropdownButton(
            items: List.generate(DateTime.now().year - 2000 + 1, (index) {
              int year = 2000 + index;
              return DropdownMenuItem(
                value: year,
                child: Text(year.toString()),
              );
            }),
            value: DateTime.now().year,
            underline: SizedBox(),
            isDense: true,
            style: TextStyle(
              fontSize: AppConstants.fontSize14,
              color: AppConstants.textMidColor,
              fontWeight: FontWeight.w700,
            ),
            icon: ReusableSvgImage(
              assetPath: "assets/icons/doctor/dropdown.svg",
              width: 20,
              height: 20,
            ),
            onChanged: (value) {},
          ),
        ),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          ),
          child: BlocBuilder<DashboardBloc, DashboardState>(
            builder: (context, state) {
              if (state is DashboardLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (state is DashboardLoaded) {
                final patientRecord = state.patientRecord;
                return SingleChildScrollView(
                  child: Column(
                    spacing: AppConstants.sizedBoxHeightMedium,
                    children: [
                      PatientRecordAnalysisWidget(
                        recordLabel: "New Patient",
                        recordValue:
                            patientRecord.currentPatientCount.toString(),
                        isPositiveTrend: patientRecord.currentPatientRatio >= 0,
                        trendPercentage:
                            (patientRecord.currentPatientRatio * 100).toInt(),
                        iconPath: "assets/icons/doctor/anonymous-user-icon.svg",
                      ),
                      PatientRecordAnalysisWidget(
                        recordLabel: "Old Patient",
                        recordValue:
                            patientRecord.previousPatientCount.toString(),
                        isPositiveTrend:
                            patientRecord.previousPatientRatio >= 0,
                        trendPercentage:
                            (patientRecord.previousPatientRatio * 100).toInt(),
                        iconPath:
                            "assets/icons/doctor/anonymous2-user-icon.svg",
                      ),
                    ],
                  ),
                );
              } else if (state is DashboardError) {
                return Center(
                  child: Text(
                    'Failed to load patient record: ${state.message}',
                  ),
                );
              } else {
                return const Center(child: Text('Unexpected state'));
              }
            },
          ),
        ),
      ),
    );
  }
}

class AskIMed extends StatelessWidget {
  const AskIMed({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 386,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF5EC9FF), Color(0xFF112C87)],
          stops: [0.0, 0.9988],
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Spacer(),
          const Text(
            'Ask iMED',
            style: TextStyle(
              fontSize: AppConstants.fontSizeExtraLarge,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
          Spacer(),
          Image.asset(
            'assets/images/robot-image-without-circle.png',
            width: 147,
          ),
        ],
      ),
    );
  }
}

class _DashboardQueryComponent extends StatelessWidget {
  const _DashboardQueryComponent();

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double minWidth = 193;
        double calculatedMaxWidth =
            (MediaQuery.of(context).size.width - 308) * 1 / 5;
        double maxWidth =
            calculatedMaxWidth <= constraints.minWidth
                ? minWidth
                : calculatedMaxWidth;
        return SizedBox(
          width: maxWidth.clamp(minWidth, double.infinity),
          height: 613,

          child: Column(
            children: const [
              PatientRecord(),
              SizedBox(height: AppConstants.sizedBoxHeightMedium),
              AskIMed(),
            ],
          ),
        );
      },
    );
  }
}

class DashboardCardsRow extends StatelessWidget {
  const DashboardCardsRow({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        const double minCardWidth = 260;
        const double spacing = AppConstants.sizedBoxHeightMedium;
        const int cardCount = 3;

        int cardsPerRow =
            (constraints.maxWidth + spacing) ~/ (minCardWidth + spacing);
        cardsPerRow = cardsPerRow.clamp(1, cardCount);

        double cardWidth =
            (constraints.maxWidth - (cardsPerRow - 1) * spacing) / cardsPerRow;
        cardWidth = cardWidth.clamp(minCardWidth, double.infinity);

        return Wrap(
          spacing: spacing,
          runSpacing: AppConstants.sizedBoxHeightMedium,
          children: [
            SizedBox(
              width: cardWidth,
              child: const _AppointmentRequestComponent(),
            ),
            SizedBox(
              width: cardWidth,
              child: const _TodayAppointmentsComponent(),
            ),
            SizedBox(width: cardWidth, child: const _DashboardQueryComponent()),
          ],
        );
      },
    );
  }
}

class _RecentPatientsComponent extends StatelessWidget {
  const _RecentPatientsComponent();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 613,
      child: CardWindow(
        title: "Recent Patients",
        child: SingleChildScrollView(
          child: BlocBuilder<DashboardBloc, DashboardState>(
            builder: (context, state) {
              if (state is DashboardLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (state is DashboardLoaded) {
                final recentPatients =
                    state.recentPatients.appointments
                        .where(
                          (appointment) => appointment.status == 'completed',
                        )
                        .map(
                          (appointment) => {
                            'image': "assets/images/person1.png",
                            'name': appointment.patientName,
                            'id': "V12",
                            'date': appointment.startTime,
                            'gender': appointment.gender,
                            'disease': appointment.reason,
                            'status': appointment.status,
                          },
                        )
                        .toList();
                if (recentPatients.isEmpty) {
                  return const Center(child: Text("No recent patients"));
                }
                // Add header row
                recentPatients.insert(0, {
                  'image': '',
                  'name': '',
                  'id': '',
                  'date': '',
                  'gender': '',
                  'disease': '',
                  'status': '',
                });

                return Table(
                  columnWidths: const {
                    0: FixedColumnWidth(50),
                    7: FixedColumnWidth(40),
                  },
                  children:
                      recentPatients.asMap().entries.map<TableRow>((entry) {
                        final patient = entry.value;
                        final isHeader = entry.key == 0;

                        return TableRow(
                          decoration:
                              isHeader
                                  ? const BoxDecoration(
                                    color: Colors.transparent,
                                  )
                                  : const BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        color: Color(0xFFE0E0E0),
                                        width: 1,
                                      ),
                                    ),
                                  ),
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: AppConstants.paddingMedium,
                              ),
                              child:
                                  isHeader
                                      ? SizedBox()
                                      : CircleAvatar(
                                        foregroundImage: AssetImage(
                                          "assets/images/person1.png",
                                        ),
                                        radius: 15,
                                      ),
                            ),
                            ...List.generate(6, (colIndex) {
                              final labels = [
                                'Patient Name',
                                'ID',
                                'Date',
                                'Gender',
                                'Disease',
                                'Status',
                              ];
                              final fields = [
                                'name',
                                'id',
                                'date',
                                'gender',
                                'disease',
                                'status',
                              ];
                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: AppConstants.paddingMedium,
                                ),
                                child: Text(
                                  isHeader
                                      ? labels[colIndex].toUpperCase()
                                      : (patient[fields[colIndex]] ?? ''),
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodyMedium?.copyWith(
                                    color:
                                        isHeader
                                            ? AppConstants.textHighColor
                                            : AppConstants.textMidColor,
                                    fontSize: 18,
                                    fontWeight:
                                        isHeader ? FontWeight.w700 : null,
                                  ),
                                ),
                              );
                            }),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: AppConstants.paddingMedium,
                              ),
                              child:
                                  isHeader
                                      ? SizedBox()
                                      : ReusableSvgImage(
                                        assetPath:
                                            "assets/icons/doctor/more_vert.svg",
                                      ),
                            ),
                          ],
                        );
                      }).toList(),
                );
              } else if (state is DashboardError) {
                return Center(
                  child: Text('Failed to load appointments: ${state.message}'),
                );
              } else {
                return const Center(child: Text('Unexpected state'));
              }
            },
          ),
        ),
      ),
    );
  }
}

class UpcomingSchedule extends StatelessWidget {
  const UpcomingSchedule({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 20),
      decoration: BoxDecoration(
        color: Color(0xFF1B2433),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Next Week',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.w700,
                ),
              ),
              SizedBox(height: AppConstants.sizedBoxHeightSmall),
              Text(
                'Upcoming Schedule',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
          Spacer(),
          IntrinsicWidth(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: AppConstants.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 14,
                  vertical: 2,
                ),
              ),
              child: Text(
                "Open",
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium!.copyWith(color: Colors.white),
              ),
              onPressed: () {},
            ),
          ),
        ],
      ),
    );
  }
}

class LabelledInfoCard extends StatefulWidget {
  final double maxWidth;
  final String title;
  final String value;
  final String iconPath;
  final Color backgroundColor;
  const LabelledInfoCard({
    super.key,
    required this.maxWidth,
    required this.title,
    required this.value,
    required this.iconPath,
    required this.backgroundColor,
  });

  @override
  State<LabelledInfoCard> createState() => _LabelledInfoCardState();
}

class _LabelledInfoCardState extends State<LabelledInfoCard> {
  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(minWidth: 200, maxWidth: widget.maxWidth),
      child: Container(
        padding: const EdgeInsets.only(
          bottom: 16,
          top: 16,
          left: 20,
          right: 27,
        ),
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: const Color(0x1A000109),
              offset: const Offset(0, 4),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              padding: const EdgeInsets.all(12),
              child: ReusableSvgImage(assetPath: widget.iconPath),
            ),
            SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    fontWeight: FontWeight.w400,
                    color: Colors.white,
                  ),
                ),
                Text(
                  widget.value,
                  style: TextStyle(
                    fontSize: 29,
                    color: Colors.white,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class PatientRecordAnalysisWidget extends StatelessWidget {
  final int trendPercentage;
  final bool isPositiveTrend;
  final String recordValue;
  final String recordLabel;
  final Color backgroundColor;
  final double borderRadius;
  final String iconPath;
  const PatientRecordAnalysisWidget({
    super.key,
    required this.recordValue,
    required this.recordLabel,
    required this.trendPercentage,
    required this.isPositiveTrend,
    this.backgroundColor = Colors.transparent,
    this.borderRadius = 10.0,
    this.iconPath = 'assets/icons/doctor/anonymous-user-icon.svg',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ReusableSvgImage(assetPath: iconPath, width: 36, height: 36),
          SizedBox(width: 8),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                recordValue,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
              ),
              SizedBox(height: 1),
              Text(
                recordLabel,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConstants.textMidColor,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          Spacer(),
          TrendStat(
            trendPercentage: trendPercentage,
            isPositiveTrend: isPositiveTrend,
          ),
        ],
      ),
    );
  }
}

class TrendStat extends StatelessWidget {
  final int trendPercentage;
  final bool isPositiveTrend;
  final Color positiveTrendColor = AppConstants.primaryColor;
  final Color negativeTrendColor = AppConstants.primaryColor;
  const TrendStat({
    super.key,
    required this.trendPercentage,
    required this.isPositiveTrend,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ReusableSvgImage(
          assetPath: 'assets/icons/doctor/trending-up-icon.svg',
          color: isPositiveTrend ? positiveTrendColor : negativeTrendColor,
        ),
        Text(
          '\$$trendPercentage%',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: isPositiveTrend ? positiveTrendColor : negativeTrendColor,
          ),
        ),
      ],
    );
  }
}

class CardWindow extends StatefulWidget {
  final String? title;
  final Widget? optionWidget;
  final Widget? child;
  final Color backgroundColor;
  final EdgeInsetsGeometry? padding;
  const CardWindow({
    super.key,
    this.title,
    this.optionWidget,
    required this.child,
    this.backgroundColor = Colors.white,
    this.padding = const EdgeInsets.all(AppConstants.paddingLarge),
  });

  @override
  State<CardWindow> createState() => _CardWindowState();
}

class _CardWindowState extends State<CardWindow> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: AppConstants.sizedBoxHeightLarge,
        children: [
          if (widget.title != null || widget.optionWidget != null)
            Row(
              children: [
                if (widget.title != null)
                  Text(
                    widget.title!,
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                Spacer(),
                if (widget.optionWidget != null) widget.optionWidget!,
              ],
            ),
          Expanded(child: widget.child!),
        ],
      ),
    );
  }
}
