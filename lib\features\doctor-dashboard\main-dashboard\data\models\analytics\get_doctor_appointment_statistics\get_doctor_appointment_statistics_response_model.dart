import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_data.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_response.dart';

class GetDoctorAppointmentStatisticsResponseModel
    extends GetDoctorAppointmentStatisticsResponse {
  const GetDoctorAppointmentStatisticsResponseModel({
    required super.data,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory GetDoctorAppointmentStatisticsResponseModel.fromJson(
    Map<String, dynamic> json,
  ) {
    return GetDoctorAppointmentStatisticsResponseModel(
      data: GetDoctorAppointmentStatisticsData(
        totalAppointments: json['data']['total_appointments'] as int,
        totalPatients: json['data']['total_patients'] as int,
        totalClinicConsulting: json['data']['total_clinic_consulting'] as int,
        totalVirtualConsulting: json['data']['total_virtual_consulting'] as int,
      ),

      message: json['message'] as String,
      status: json['status'] as String,
      statusCode: json['statusCode'] as int,
    );
  }

  factory GetDoctorAppointmentStatisticsResponseModel.fromEntity(
    GetDoctorAppointmentStatisticsResponse entity,
  ) {
    return GetDoctorAppointmentStatisticsResponseModel(
      data: entity.data,
      message: entity.message,
      status: entity.status,
      statusCode: entity.statusCode,
    );
  }
}
