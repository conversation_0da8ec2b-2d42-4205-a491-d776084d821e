import 'package:equatable/equatable.dart';

class GetAppointmentsByDoctorData extends Equatable {
  final String time;
  final String bookingID;
  final String? appointmentID;
  final String disease;
  final String phone;
  final String status;

  const GetAppointmentsByDoctorData({
    required this.time,
    required this.bookingID,
    this.appointmentID = "",
    required this.disease,
    required this.phone,
    required this.status,
  });

  @override
  List<Object?> get props => [
    time,
    bookingID,
    appointmentID,
    disease,
    phone,
    status,
  ];
}
