import 'package:equatable/equatable.dart';

class GetDesignationData extends Equatable {
  final List<GetDesignationDataItems> designations;

  const GetDesignationData({required this.designations});

  @override
  List<Object?> get props => [designations];
}

class GetDesignationDataItems extends Equatable {
  final String value;
  final String label;

  const GetDesignationDataItems({required this.value, required this.label});
  @override
  List<Object?> get props => [value, label];
}
