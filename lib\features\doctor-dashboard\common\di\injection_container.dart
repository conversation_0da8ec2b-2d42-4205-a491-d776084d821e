import 'package:get_it/get_it.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_all_address_type_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_departments_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_designation_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_hospitals_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_investigation_list_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_all_address_type_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_departments_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_designations_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_hospitals_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/repositories/get_investigation_list_repositories_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_all_address_type_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_departments_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_designations_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_hospitals_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_investigation_list_repositories.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_all_address_type.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_departments.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_designations.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_hospitals.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/usecases/get_investigation_list.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/bloc/dropdown_items/dropdown_items_bloc.dart';

final sl = GetIt.instance;

void injectCommon() {
  sl.registerFactory(
    () => DropdownItemsBloc(
      getAllAddressTypeRemoteDatasource: sl(),
      getDepartmentsRemoteDatasource: sl(),
      getDesignationsRemoteDatasource: sl(),
      getHospitalsRemoteDatasource: sl(),
      getInvestigationListRemoteDatasource: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetAllAddressType(sl()));
  sl.registerLazySingleton(() => GetDepartments(sl()));
  sl.registerLazySingleton(() => GetDesignations(sl()));
  sl.registerLazySingleton(() => GetHospitals(sl()));
  sl.registerLazySingleton(() => GetInvestigationList(sl()));

  // Repositories
  sl.registerLazySingleton<GetAllAddressTypeRepositories>(
    () => GetAllAddressTypeRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<GetDepartmentsRepositories>(
    () => GetDepartmentsRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<GetDesignationsRepositories>(
    () => GetDesignationsRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<GetHospitalsRepositories>(
    () => GetHospitalsRepositoriesImpl(remoteDatasource: sl()),
  );
  sl.registerLazySingleton<GetInvestigationListRepositories>(
    () => GetInvestigationListRepositoriesImpl(remoteDatasource: sl()),
  );

  // Data sources
  sl.registerLazySingleton<GetAllAddressTypeRemoteDatasource>(
    () => GetAllAddressTypeRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<GetDepartmentsRemoteDatasource>(
    () => GetDepartmentsRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<GetDesignationsRemoteDatasource>(
    () => GetDesignationsRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<GetHospitalsRemoteDatasource>(
    () => GetHospitalsRemoteDatasourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<GetInvestigationListRemoteDatasource>(
    () => GetInvestigationListRemoteDatasourceImpl(dioClient: sl()),
  );
}
