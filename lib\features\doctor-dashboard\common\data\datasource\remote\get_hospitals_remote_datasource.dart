import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_hospitals/get_hospitals_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_hospitals/get_hospitals_response_model.dart';

abstract class GetHospitalsRemoteDatasource {
  Future<GetHospitalsResponseModel> getHospitals(
    GetHospitalsRequestModel requestModel,
  );
}

class GetHospitalsRemoteDatasourceImpl implements GetHospitalsRemoteDatasource {
  final DioClient dioClient;

  GetHospitalsRemoteDatasourceImpl({required this.dioClient});

  @override
  Future<GetHospitalsResponseModel> getHospitals(
    GetHospitalsRequestModel request,
  ) async {
    try {
      // final queryParams = request.toJson();

      // final response = await dioClient.get(
      //   '{{devURL}}/doctors/hospitals?skip=2&limit=5',
      // );
      // return GetHospitalsResponseModel.fromJson(response.data);

      await Future.delayed(const Duration(seconds: 2));

      final responseJson = {
        "data": [
          {
            "value": "1feec337-1a33-4c7a-9278-75c5c6a344a8",
            "label": "Max Hospital Limited",
          },
          {
            "value": "56dcc8db-cc99-41be-b216-a9e0ec2ffff1",
            "label": "Lakeside Community Hospital",
          },
          {
            "value": "34291020-b0d5-4ea1-b526-6f120b485fa3",
            "label": "Hopewell Medical Institute",
          },
          {
            "value": "7dc44de7-c472-498c-b554-ff5b1814b918",
            "label": "Riverbend Hospital",
          },
          {
            "value": "4df72351-4867-4ca0-a4e0-938311ed682d",
            "label": "Pinecrest Medical Center",
          },
          {
            "value": "7944591a-2f81-40f1-8860-1e86b38378ca",
            "label": "Green Valley Medical Center",
          },
        ],
        "total": 15,
        "skip": 2,
        "limit": 5,
        "message": "Hospitals fetched successfully.",
        "status": "success",
        "statusCode": 200,
      };

      return GetHospitalsResponseModel.fromJson(responseJson);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
