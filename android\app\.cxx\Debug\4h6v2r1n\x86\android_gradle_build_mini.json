{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\development\\professinal project\\imed_fe\\android\\app\\.cxx\\Debug\\4h6v2r1n\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\development\\professinal project\\imed_fe\\android\\app\\.cxx\\Debug\\4h6v2r1n\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}