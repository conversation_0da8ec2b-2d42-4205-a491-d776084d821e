import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/utils/file_picker_utils.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'labelled_widget.dart';

class CustomFilePickerBox extends StatelessWidget {
  final String? label;
  final String? errorText;
  final String uploadText;
  final String? prefixIcon;

  const CustomFilePickerBox({
    super.key,
    this.label,
    this.errorText,
    this.uploadText = "Upload Document",
    this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return LabelledWidget(
      label: label ?? "",
      isRequired: true,
      errorText: errorText,
      border: BorderSide(
        style: BorderStyle.solid,
        color: AppConstants.borderColor,
      ),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      child: GestureDetector(
        onTap: () {
          FilePickerUtils.pickSingleFile(
            allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'png'],
            type: FileType.custom,
          );
        },
        child: Row(
          children: [
            if (prefixIcon != null) ...[
              ReusableSvgImage(assetPath: prefixIcon!),
              SizedBox(width: 12),
            ],
            Expanded(
              child: Text(
                uploadText,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: AppConstants.inputFieldForegroundColor,
                ),
              ),
            ),
            ReusableSvgImage(assetPath: "assets/icons/doctor/upload-icon.svg"),
          ],
        ),
      ),
    );
  }
}