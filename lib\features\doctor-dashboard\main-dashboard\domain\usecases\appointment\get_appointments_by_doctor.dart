import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_by_doctor/get_appointments_by_doctor_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointments_by_doctor/get_appointments_by_doctor_request.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointments_by_doctor/get_appointments_by_doctor_response.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/appointments/get_appointments_by_doctor_repositories.dart';

class GetAppointmentsByDoctor
    implements
        UseCase<
          GetAppointmentsByDoctorResponse,
          GetAppointmentsByDoctorRequest
        > {
  final GetAppointmentByDoctorRepositories repository;

  GetAppointmentsByDoctor(this.repository);

  @override
  Future<Either<Failure, GetAppointmentsByDoctorResponse>> call(
    GetAppointmentsByDoctorRequest params,
  ) async {
    final requestModel = GetAppointmentsByDoctorRequestModel.fromEntity(params);
    return await repository.getAppointmentsByDoctor(requestModel);
  }
}
