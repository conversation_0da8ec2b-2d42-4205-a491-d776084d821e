import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/datasource/remote/analytics/get_doctor_appointment_statistics_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_response_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/repositories/analytics/get_doctor_appointment_statistics_repositories.dart';

class GetDoctorAppointmentStatisticsRepositoriesImpl
    implements GetDoctorAppointmentStatisticsRepositories {
  final GetDoctorAppointmentStatisticsRemoteDatasource remoteDatasource;

  GetDoctorAppointmentStatisticsRepositoriesImpl({
    required this.remoteDatasource,
  });

  @override
  Future<Either<Failure, GetDoctorAppointmentStatisticsResponseModel>>
  getDoctorAppointmentStatistics(
    GetDoctorAppointmentStatisticsRequestModel request,
  ) async {
    try {
      final response = await remoteDatasource.getDoctorAppointmentStatistics(
        request,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
