import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import '../entities/time_slot.dart';
import '../repositories/availability_repository.dart';

class GetAvailableTimeSlots implements UseCase<List<TimeSlot>, GetAvailableTimeSlotsParams> {
  final AvailabilityRepository repository;

  GetAvailableTimeSlots(this.repository);

  @override
  Future<Either<Failure, List<TimeSlot>>> call(GetAvailableTimeSlotsParams params) async {
    return await repository.getAvailableTimeSlots(
      doctorId: params.doctorId,
      date: params.date,
    );
  }
}

class GetAvailableTimeSlotsParams extends Equatable {
  final String doctorId;
  final DateTime date;

  const GetAvailableTimeSlotsParams({
    required this.doctorId,
    required this.date,
  });

  @override
  List<Object?> get props => [doctorId, date];
}
