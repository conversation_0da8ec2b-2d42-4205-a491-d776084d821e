import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_all_address_type/get_all_address_type_data.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_all_address_type/get_all_address_type_response.dart';

class GetAllAddressTypeResponseModel extends GetAllAddressTypeResponse {
  const GetAllAddressTypeResponseModel({
    required super.data,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory GetAllAddressTypeResponseModel.fromJson(Map<String, dynamic> json) {
    return GetAllAddressTypeResponseModel(
      data: GetAllAddressTypeData(
        data:
            (json['data'] as List)
                .map(
                  (item) => GetAllAddressTypeDataItems(
                    id: item['id'] as String,
                    code: item['code'] as String,
                    label: item['label'] as String,
                    description: item['description'] as String?,
                  ),
                )
                .toList(),
      ),
      message: json['message'] as String,
      status: json['status'] as String,
      statusCode: json['statusCode'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "data":
          data.data
              .map(
                (item) => {
                  "id": item.id,
                  "code": item.code,
                  "label": item.label,
                  "description": item.description,
                },
              )
              .toList(),
      "message": message,
      "status": status,
      "statusCode": statusCode,
    };
  }
}
