{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\development\\professinal project\\imed_fe\\android\\app\\.cxx\\Debug\\3r5843h1\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\development\\professinal project\\imed_fe\\android\\app\\.cxx\\Debug\\3r5843h1\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}