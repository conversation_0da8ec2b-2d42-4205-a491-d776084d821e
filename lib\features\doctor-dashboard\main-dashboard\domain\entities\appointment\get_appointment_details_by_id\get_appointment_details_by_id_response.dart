import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointment_details_by_id/get_appointment_details_by_id_data.dart';

class GetAppointmentDetailsByIDResponse extends Equatable {
  final GetAppointmentDetailsByIDData data;
  final String message;
  final String status;
  final int statusCode;

  const GetAppointmentDetailsByIDResponse({
    required this.data,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  @override
  List<Object?> get props => [data, message, status, statusCode];
}
