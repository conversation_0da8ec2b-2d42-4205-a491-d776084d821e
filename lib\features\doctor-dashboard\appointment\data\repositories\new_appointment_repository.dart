import '../models/new_appointment_model.dart';
import '../../domain/entities/new_appointment_request.dart';

abstract class NewAppointmentRepository {
  Future<NewAppointmentModel> createAppointment(NewAppointmentRequest request);
}

class NewAppointmentRepositoryImpl implements NewAppointmentRepository {
  @override
  Future<NewAppointmentModel> createAppointment(NewAppointmentRequest request) async {
    await Future.delayed(const Duration(seconds: 2));


    const dummyResponse = {
      "data": {
        "booking_id": "WALKIN-S2L0FYJ8",
        "slot_date": "2025-10-25",
        "start_time": "19:00:00",
        "end_time": "19:30:00"
      },
      "message": "Walk-in Patient and Appointment created successfully",
      "status": "success",
      "statusCode": 201
    };

    if (request.firstName.toLowerCase() == 'error') {
      throw Exception('Simulated API Error: Invalid data provided.');
    }

    return NewAppointmentModel.from<PERSON>son(dummyResponse);
  }
}