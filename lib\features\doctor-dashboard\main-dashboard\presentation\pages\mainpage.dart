import 'package:flutter/material.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/widgets/sidebar.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/widgets/wappbar.dart';

class MainPage extends StatefulWidget {
  final Widget child;
  const MainPage({super.key, required this.child});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          SideBar(
            menuItems: [
              SideBarListItem(
                icon: "assets/icons/doctor/dashboard.svg",
                title: 'Dashboard',
                route: AppRouter.dashboard,
              ),
              SideBarListItem(
                icon: "assets/icons/doctor/patients.svg",
                title: 'Our Patients',
                route: AppRouter.patients,
                subItems: [
                  SideBarListItem(
                    icon: "assets/icons/doctor/patients.svg",
                    title: 'All Patients',
                    route: AppRouter.patients,
                  ),
                  SideBarListItem(
                    icon: "assets/icons/doctor/patients.svg",
                    title: 'Add Patient',
                    route: '${AppRouter.patients}/add',
                  ),
                ],
              ),
              SideBarListItem(
                icon: "assets/icons/doctor/calendar.svg",
                title: 'Calendar',
                route: AppRouter.calendar,
              ),
              SideBarListItem(
                icon: "assets/icons/doctor/availability.svg",
                title: 'Availability',
                route: 'AppRouter.availability',
              ),
              SideBarListItem(
                icon: "assets/icons/doctor/appointment.svg",
                title: 'Appointment',
                route: 'AppRouter.appointment',
                subItems: [
                  SideBarListItem(
                    icon: "assets/icons/doctor/appointment.svg",
                    title: 'Upcoming',
                    route: 'AppRouter.appointment',
                  ),
                  SideBarListItem(
                    icon: "assets/icons/doctor/appointment.svg",
                    title: 'Past',
                    route: 'AppRouter.appointment',
                  ),
                  SideBarListItem(
                    icon: "assets/icons/doctor/appointment.svg",
                    title: 'Cancelled',
                    route: 'AppRouter.appointment',
                  ),
                ],
              ),
              SideBarListItem(
                icon: "assets/icons/doctor/message.svg",
                title: 'Message',
                route: 'AppRouter.message',
              ),
              SideBarListItem(
                icon: "assets/icons/doctor/profile.svg",
                title: 'My Profile',
                route: AppRouter.myProfile,
              ),
              SideBarListItem(
                icon: "assets/icons/doctor/reviews.svg",
                title: 'Reviews',
                route: 'AppRouter.reviews',
              ),
              SideBarListItem(
                icon: "assets/icons/doctor/analytics.svg",
                title: 'Analytics',
                route: 'AppRouter.analytics',
              ),
              SideBarListItem(
                icon: "assets/icons/doctor/settings.svg",
                title: 'Settings',
                route: 'AppRouter.settings',
              ),
            ],
          ),
          Expanded(
            child: Column(
              children: [const WAppBar(), Expanded(child: widget.child)],
            ),
          ),
        ],
      ),
    );
  }
}
