import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_all_address_type/get_all_address_type_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_all_address_type/get_all_address_type_response_model.dart';

abstract class GetAllAddressTypeRemoteDatasource {
  Future<GetAllAddressTypeResponseModel> getAllAddressType(
    GetAllAddressTypeRequestModel requestModel,
  );
}

class GetAllAddressTypeRemoteDatasourceImpl
    implements GetAllAddressTypeRemoteDatasource {
  final DioClient dioClient;

  GetAllAddressTypeRemoteDatasourceImpl({required this.dioClient});

  @override
  Future<GetAllAddressTypeResponseModel> getAllAddressType(
    GetAllAddressTypeRequestModel request,
  ) async {
    try {
      // final queryParams = request.toJson();

      // final response = await dioClient.get(
      //   '{{utilityURL}}/utilities/address-types',
      // );
      // return GetDoctorProfileInfoResponseModel.fromJson(response.data);

      await Future.delayed(const Duration(seconds: 2));

      final responseJson = {
        "data": [
          {
            "id": "a983e3c3-b97a-47f4-8eeb-f9fc45feec68",
            "code": "home",
            "label": "home address",
            "description": null,
          },
          {
            "id": "55f49b6c-95a1-4859-96c1-aea2d79cb5d0",
            "code": "office",
            "label": "office address",
            "description": null,
          },
          {
            "id": "48069b20-511f-42f7-85b4-f076046b280d",
            "code": "other",
            "label": "other address",
            "description": null,
          },
        ],
        "message": "Address types fetched successfully",
        "status": "success",
        "statusCode": 200,
      };

      return GetAllAddressTypeResponseModel.fromJson(responseJson);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
