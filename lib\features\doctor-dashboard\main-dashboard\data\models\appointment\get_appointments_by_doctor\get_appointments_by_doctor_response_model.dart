import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointments_by_doctor/get_appointments_by_doctor_data.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointments_by_doctor/get_appointments_by_doctor_response.dart';

class GetAppointmentsByDoctorResponseModel
    extends GetAppointmentsByDoctorResponse {
  const GetAppointmentsByDoctorResponseModel({
    required super.data,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory GetAppointmentsByDoctorResponseModel.fromJson(
    Map<String, dynamic> json,
  ) {
    return GetAppointmentsByDoctorResponseModel(
      data:
          (json['data'] as List<dynamic>).map((appointment) {
            final map = appointment as Map<String, dynamic>;
            return GetAppointmentsByDoctorData(
              time: map['time'] as String,
              bookingID: map['booking_id'] as String,
              appointmentID:
                  map.containsKey('appointment_id')
                      ? (map['appointment_id'] as String?) ?? ''
                      : '',
              disease: map['disease'] as String,
              phone: map['phone'] as String,
              status: map['status'] as String,
            );
          }).toList(),
      message: json['message'] as String,
      status: json['status'] as String,
      statusCode: json['statusCode'] as int,
    );
  }

  factory GetAppointmentsByDoctorResponseModel.fromEntity(
    GetAppointmentsByDoctorResponse entity,
  ) {
    return GetAppointmentsByDoctorResponseModel(
      data: entity.data,
      message: entity.message,
      status: entity.status,
      statusCode: entity.statusCode,
    );
  }
}
