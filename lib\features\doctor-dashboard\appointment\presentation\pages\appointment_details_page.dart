import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/di/injection_container.dart';
import '../../../../../core/widgets/custom_elevated_button.dart';
import '../../../../../core/widgets/reusable_icon_button.dart';
import '../../data/models/appointment_detail_model.dart';
import '../bloc/appointment_bloc.dart';

const double _breakpoint = 750.0;

class AppointmentsDetailPage extends StatelessWidget {
  final String appointmentId;

  const AppointmentsDetailPage({
    super.key,
    required this.appointmentId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<AppointmentBloc>()
        ..add(FetchAppointmentDetailEvent(appointmentId)),
      child: Scaffold(
        backgroundColor: AppConstants.backgroundColor,
        body: BlocBuilder<AppointmentBloc, AppointmentState>(
          builder: (context, state) {
            return LayoutBuilder(
              builder: (context, constraints) {
                final isDesktop = constraints.maxWidth >= _breakpoint;

                if (state is AppointmentDetailLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is AppointmentError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          state.message,
                          style: TextStyle(
                            color: AppConstants.errorColor,
                            fontSize: AppConstants.fontSizeLarge,
                          ),
                        ),
                        const SizedBox(height: AppConstants.paddingMedium),
                        ElevatedButton(
                          onPressed: () {
                            context.read<AppointmentBloc>().add(
                              FetchAppointmentDetailEvent(appointmentId),
                            );
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                if (state is AppointmentDetailLoaded) {
                  // state.appointmentDetail is already the domain entity, no need to convert
                  final appointmentDetail = state.appointmentDetail;
                  return _buildContent(context, appointmentDetail, isDesktop);
                }

                return const SizedBox();
              },
            );
          },
        ),
      ),
    );
  }

  // ... rest of your _buildContent, _buildPatientInfoCard, etc. methods remain the same
  Widget _buildContent(BuildContext context, AppointmentDetail appointment, bool isDesktop) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: isDesktop ? AppConstants.paddingExtraLarge : AppConstants.paddingMedium,
          vertical: isDesktop ? AppConstants.paddingExtraLarge : AppConstants.paddingLarge,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeaderAndActions(context, isDesktop),
            const SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),

            _buildPatientInfoCard(context, appointment, isDesktop),
            const SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),

            _buildComplaintsSection(appointment, isDesktop),
            const SizedBox(height: AppConstants.sizedBoxHeightExtraLarge),

            _buildAttachmentsSection(appointment, isDesktop),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderAndActions(BuildContext context, bool isDesktop) {
    final title = Text(
      'Patient Details',
      style: TextStyle(
        fontSize: isDesktop ? AppConstants.fontSizeExtraLarge : AppConstants.fontSize22,
        fontWeight: FontWeight.bold,
        color: AppConstants.textPrimaryColor,
      ),
    );

    final backButton = ReusableIconButton(
      icon: Icons.arrow_back,
      onPressed: () {
        context.go('/appointments');
      },
      color: AppConstants.textPrimaryColor,
    );

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        backButton,
        const SizedBox(width: AppConstants.paddingSmall),
        title,
      ],
    );
  }

  Widget _buildPatientInfoCard(BuildContext context, AppointmentDetail appointment, bool isDesktop) {
    final patientProfile = Row(
      children: [
        CircleAvatar(
          radius: 36,
          backgroundColor: AppConstants.inputFieldBackgroundColor,
          backgroundImage: const NetworkImage('https://placehold.co/100x100/A8C5E5/FFFFFF?text=JD'),
        ),
        const SizedBox(width: AppConstants.paddingMedium),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              appointment.patientName,
              style: TextStyle(
                fontSize: AppConstants.fontSize20,
                fontWeight: FontWeight.bold,
                color: AppConstants.textPrimaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              'Age ${appointment.age} • ${_getAppointmentType(appointment.appointmentType)}',
              style: TextStyle(
                fontSize: AppConstants.fontSize14,
                color: AppConstants.textMidColor,
                height: 1.4,
              ),
            ),
          ],
        ),
      ],
    );

    final contactDetails = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          appointment.patientPhone,
          style: TextStyle(
            color: AppConstants.textHighColor,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
      ],
    );

    final appointmentTime = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDateTimeChip(appointment.formattedDate, Icons.calendar_today_outlined),
        const SizedBox(height: AppConstants.paddingSmall),
        _buildDateTimeChip(appointment.formattedTime, Icons.access_time_rounded),
      ],
    );

    final actionButtons = Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomElevatedButton(
          onPressed: () {
            _handleAppointmentAction(context, 'confirm', appointment.appointmentId);
          },
          text: 'Confirm Appointment',
          backgroundColor: AppConstants.elevatedButtonBackgroundColor,
          foregroundColor: AppConstants.elevatedButtonForegroundColor,
          borderRadius: AppConstants.borderRadiusSmall,
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: FontWeight.w500,
          prefixIcon: Icon(
            Icons.check_circle_outline,
            size: 20,
            color: AppConstants.elevatedButtonForegroundColor,
          ),
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightMedium),
        CustomElevatedButton(
          onPressed: () {
            _handleAppointmentAction(context, 'decline', appointment.appointmentId);
          },
          text: 'Decline Appointment',
          backgroundColor: AppConstants.errorColor.withOpacity(0.1),
          foregroundColor: AppConstants.errorColor,
          borderRadius: AppConstants.borderRadiusSmall,
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: FontWeight.w500,
          prefixIcon: Icon(
            Icons.close_rounded,
            size: 20,
            color: AppConstants.errorColor,
          ),
        ),
      ],
    );

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          isDesktop
              ? Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: 3, child: patientProfile),
              Expanded(flex: 2, child: contactDetails),
              Expanded(flex: 2, child: appointmentTime),
              Expanded(flex: 3, child: actionButtons),
            ],
          )
              : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              patientProfile,
              const SizedBox(height: AppConstants.paddingMedium),
              contactDetails,
              const SizedBox(height: AppConstants.paddingMedium),
              appointmentTime,
              const SizedBox(height: AppConstants.paddingMedium),
              actionButtons,
            ],
          ),

          const Divider(
            height: AppConstants.sizedBoxHeightExtraLarge,
            thickness: 1,
            color: AppConstants.borderColor,
          ),
          _buildContactMethods(appointment),
        ],
      ),
    );
  }

  String _getAppointmentType(String type) {
    switch (type.toLowerCase()) {
      case 'virtual':
        return 'Virtual Consultation';
      case 'in_person':
      case 'in-person':
        return 'In-Person Visit';
      case 'consultation':
        return 'Consultation';
      default:
        return type;
    }
  }

  void _handleAppointmentAction(BuildContext context, String action, String appointmentId) {
    if (action == 'confirm') {
      context.read<AppointmentBloc>().add(ConfirmAppointmentEvent(appointmentId));
    } else {
      context.read<AppointmentBloc>().add(CancelAppointmentEvent(appointmentId));
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Appointment ${action}ed'),
        backgroundColor: action == 'confirm' ? Colors.green : AppConstants.errorColor,
      ),
    );
  }

  Widget _buildDateTimeChip(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: AppConstants.paddingSmall,
      ),
      decoration: BoxDecoration(
        color: AppConstants.inputFieldBackgroundColor2,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          Text(
            text,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: AppConstants.textHighColor,
              fontSize: AppConstants.fontSize14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactMethods(AppointmentDetail appointment) {
    return Row(
      children: [
        if (appointment.appointmentType.toLowerCase() == 'virtual') ...[
          const _ContactLink(label: 'Video Call', isPrimary: true),
          const SizedBox(width: AppConstants.paddingMedium),
        ],
        const _ContactLink(label: 'Voice Call'),
        const SizedBox(width: AppConstants.paddingMedium),
      ],
    );
  }

  Widget _buildComplaintsSection(AppointmentDetail appointment, bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: AppConstants.primaryColor,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.assignment_outlined,
                size: 16,
                color: Colors.white,
              ),
              const SizedBox(width: 6),
              Text(
                'Complaints',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightMedium),

        Wrap(
          spacing: AppConstants.sizedBoxHeightMedium,
          runSpacing: AppConstants.paddingSmall,
          children: appointment.complaints
              .map((complaint) => _Tag(label: complaint.label, isSelected: false))
              .toList(),
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightLarge),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          ),
          child: Text(
            'Problem Description',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
              color: AppConstants.textHighColor,
            ),
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          appointment.reason,
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            color: AppConstants.textHighColor,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildAttachmentsSection(AppointmentDetail appointment, bool isDesktop) {
    if (appointment.files.isEmpty) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: AppConstants.primaryColor,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.attachment,
                size: 16,
                color: Colors.white,
              ),
              const SizedBox(width: 6),
              Text(
                'Attachments',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppConstants.sizedBoxHeightMedium),

        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: appointment.files
                .map((file) => _AttachmentThumbnail(fileName: file.fileName))
                .toList(),
          ),
        ),
      ],
    );
  }
}

class _ContactLink extends StatelessWidget {
  final String label;
  final bool isPrimary;

  const _ContactLink({required this.label, this.isPrimary = false});

  @override
  Widget build(BuildContext context) {
    return Text(
      label,
      style: TextStyle(
        color: isPrimary ? AppConstants.primaryColor : AppConstants.textMidColor,
        fontWeight: FontWeight.w500,
        decoration: TextDecoration.underline,
        decorationColor: isPrimary ? AppConstants.primaryColor : AppConstants.textMidColor,
        fontSize: AppConstants.fontSize14,
      ),
    );
  }
}

class _Tag extends StatelessWidget {
  final String label;
  final bool isSelected;

  const _Tag({required this.label, this.isSelected = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      decoration: BoxDecoration(
        color: isSelected ? AppConstants.primaryColor : Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(
          color: isSelected ? AppConstants.primaryColor : AppConstants.borderColor,
          width: 1,
        ),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : AppConstants.textHighColor,
          fontWeight: FontWeight.w500,
          fontSize: AppConstants.fontSize14,
        ),
      ),
    );
  }
}

class _AttachmentThumbnail extends StatelessWidget {
  final String fileName;

  const _AttachmentThumbnail({required this.fileName});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: 100,
        height: 120,
        margin: const EdgeInsets.only(right: AppConstants.sizedBoxHeightMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          border: Border.all(color: AppConstants.borderColor),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description,
              size: 40,
              color: AppConstants.errorColor,
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              fileName,
              style: TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textMidColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
    );
    }
}