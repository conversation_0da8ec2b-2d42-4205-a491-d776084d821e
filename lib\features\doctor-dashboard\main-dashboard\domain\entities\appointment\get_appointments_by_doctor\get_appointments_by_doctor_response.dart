import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/get_appointments_by_doctor/get_appointments_by_doctor_data.dart';

class GetAppointmentsByDoctorResponse extends Equatable {
  final List<GetAppointmentsByDoctorData> data;
  final String message;
  final String status;
  final int statusCode;

  const GetAppointmentsByDoctorResponse({
    required this.data,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  @override
  List<Object?> get props => [data, message, status, statusCode];
}
