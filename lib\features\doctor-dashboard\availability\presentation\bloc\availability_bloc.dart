import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/appointment.dart';
import '../../domain/usecases/create_appointment.dart';
import '../../domain/usecases/get_available_time_slots.dart';
import '../../domain/usecases/get_doctor_availability.dart';
import '../../domain/usecases/set_doctor_availability_schedule.dart';
import 'availability_event.dart';
import 'availability_state.dart';

class AvailabilityBloc extends Bloc<AvailabilityEvent, AvailabilityState> {
  final CreateAppointment createAppointment;
  final GetDoctorAvailability getDoctorAvailability;
  final GetAvailableTimeSlots getAvailableTimeSlots;
  final SetDoctorAvailabilitySchedule setDoctorAvailabilitySchedule;

  AvailabilityBloc({
    required this.createAppointment,
    required this.getDoctorAvailability,
    required this.getAvailableTimeSlots,
    required this.setDoctorAvailabilitySchedule,
  }) : super(const AvailabilityInitial()) {
    on<LoadDoctorAvailability>(_onLoadDoctorAvailability);
    on<LoadAvailableTimeSlots>(_onLoadAvailableTimeSlots);
    on<CreateAppointmentEvent>(_onCreateAppointment);
    on<SaveDoctorAvailabilitySchedule>(_onSaveDoctorAvailabilitySchedule);
    on<SelectTimeSlot>(_onSelectTimeSlot);
    on<UpdateAppointmentForm>(_onUpdateAppointmentForm);
    on<ResetAvailabilityState>(_onResetAvailabilityState);
  }

  Future<void> _onLoadDoctorAvailability(
    LoadDoctorAvailability event,
    Emitter<AvailabilityState> emit,
  ) async {
    emit(const AvailabilityLoading());

    final result = await getDoctorAvailability(
      GetDoctorAvailabilityParams(doctorId: event.doctorId, date: event.date),
    );

    result.fold(
      (failure) => emit(AvailabilityError(message: failure.message)),
      (availability) => emit(
        AvailabilityLoaded(
          doctorAvailability: availability,
          availableTimeSlots: availability.availableTimeSlots,
        ),
      ),
    );
  }

  Future<void> _onLoadAvailableTimeSlots(
    LoadAvailableTimeSlots event,
    Emitter<AvailabilityState> emit,
  ) async {
    emit(const AvailabilityLoading());

    final result = await getAvailableTimeSlots(
      GetAvailableTimeSlotsParams(doctorId: event.doctorId, date: event.date),
    );

    result.fold(
      (failure) => emit(AvailabilityError(message: failure.message)),
      (timeSlots) {
        final currentState = state;
        if (currentState is AvailabilityLoaded) {
          emit(currentState.copyWith(availableTimeSlots: timeSlots));
        } else {
          emit(AvailabilityLoaded(availableTimeSlots: timeSlots));
        }
      },
    );
  }

  Future<void> _onCreateAppointment(
    CreateAppointmentEvent event,
    Emitter<AvailabilityState> emit,
  ) async {
    emit(const AppointmentCreating());

    final result = await createAppointment(
      CreateAppointmentParams(appointment: event.appointment),
    );

    result.fold(
      (failure) => emit(AvailabilityError(message: failure.message)),
      (appointment) => emit(AppointmentCreated(appointment: appointment)),
    );
  }

  void _onSelectTimeSlot(
    SelectTimeSlot event,
    Emitter<AvailabilityState> emit,
  ) {
    final currentState = state;
    if (currentState is AvailabilityLoaded) {
      emit(currentState.copyWith(selectedTimeSlotId: event.timeSlotId));
    }
  }

  void _onUpdateAppointmentForm(
    UpdateAppointmentForm event,
    Emitter<AvailabilityState> emit,
  ) {
    final currentState = state;
    if (currentState is AvailabilityLoaded) {
      final updatedFormData = currentState.formData.copyWith(
        doctorId: event.doctorId,
        patientName: event.patientName,
        age: event.age,
        appointmentType: event.appointmentType,
        complaintType: event.complaintType,
        reason: event.reason,
      );
      emit(currentState.copyWith(formData: updatedFormData));
    } else {
      final formData = AppointmentFormData(
        doctorId: event.doctorId,
        patientName: event.patientName,
        age: event.age,
        appointmentType: event.appointmentType,
        complaintType: event.complaintType,
        reason: event.reason,
      );
      emit(AvailabilityLoaded(formData: formData));
    }
  }

  void _onResetAvailabilityState(
    ResetAvailabilityState event,
    Emitter<AvailabilityState> emit,
  ) {
    emit(const AvailabilityInitial());
  }

  /// Handles saving doctor availability schedule
  Future<void> _onSaveDoctorAvailabilitySchedule(
    SaveDoctorAvailabilitySchedule event,
    Emitter<AvailabilityState> emit,
  ) async {
    print('🔄 BLoC: Starting to save doctor availability schedule');
    emit(const AvailabilitySaving());

    final result = await setDoctorAvailabilitySchedule(
      SetDoctorAvailabilityScheduleParams(schedule: event.schedule),
    );

    result.fold(
      (failure) {
        print(
          '❌ BLoC: Failed to save availability schedule - ${failure.message}',
        );
        emit(AvailabilityError(message: failure.message));
      },
      (schedule) {
        print('✅ BLoC: Successfully saved availability schedule');
        print('📦 BLoC: Saved schedule data: $schedule');
        emit(AvailabilitySaved(schedule: schedule));
      },
    );
  }
}
