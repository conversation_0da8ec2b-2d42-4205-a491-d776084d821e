import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_designation/get_designation_data.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_designation/get_designation_response.dart';

class GetDesignationsResponseModel extends GetDesignationResponse {
  const GetDesignationsResponseModel({
    required super.data,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory GetDesignationsResponseModel.fromJson(Map<String, dynamic> json) {
    return GetDesignationsResponseModel(
      data: GetDesignationData(
        designations:
            (json['data'] as List)
                .map(
                  (item) => GetDesignationDataItems(
                    value: item['value'] as String,
                    label: item['label'] as String,
                  ),
                )
                .toList(),
      ),
      message: json['message'],
      status: json['status'],
      statusCode: json['statusCode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data':
          data.designations
              .map((item) => {'value': item.value, 'label': item.label})
              .toList(),
      'message': message,
      'status': status,
      'statusCode': statusCode,
    };
  }
}
