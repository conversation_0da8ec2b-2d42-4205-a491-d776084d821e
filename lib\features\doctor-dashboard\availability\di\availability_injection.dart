import 'package:get_it/get_it.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/data/datasources/availability_remote_data_source.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/data/repositories/availability_repository_impl.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/domain/repositories/availability_repository.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/domain/usecases/create_appointment.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/domain/usecases/get_available_time_slots.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/domain/usecases/get_doctor_availability.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/domain/usecases/set_doctor_availability_schedule.dart';
import 'package:imed_fe/features/doctor-dashboard/availability/presentation/bloc/availability_bloc.dart';

Future<void> injectAvailability(GetIt sl) async {
  //Blocs
  sl.registerFactory(
    () => AvailabilityBloc(
      createAppointment: sl(),
      getDoctorAvailability: sl(),
      getAvailableTimeSlots: sl(),
      setDoctorAvailabilitySchedule: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => CreateAppointment(sl()));
  sl.registerLazySingleton(() => GetDoctorAvailability(sl()));
  sl.registerLazySingleton(() => GetAvailableTimeSlots(sl()));
  sl.registerLazySingleton(() => SetDoctorAvailabilitySchedule(sl()));

  //Repositories
  sl.registerLazySingleton<AvailabilityRepository>(
    () => AvailabilityRepositoryImpl(remoteDataSource: sl(), networkInfo: sl()),
  );

  // Data sources
  sl.registerLazySingleton<AvailabilityRemoteDataSource>(
    () => AvailabilityRemoteDataSourceImpl(dioClient: sl()),
  );
}
