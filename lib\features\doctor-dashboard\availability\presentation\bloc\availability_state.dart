import 'package:equatable/equatable.dart';
import '../../domain/entities/appointment.dart';
import '../../domain/entities/doctor_availability.dart';
import '../../domain/entities/doctor_availability_schedule.dart';
import '../../domain/entities/time_slot.dart';

abstract class AvailabilityState extends Equatable {
  const AvailabilityState();

  @override
  List<Object?> get props => [];
}

class AvailabilityInitial extends AvailabilityState {
  const AvailabilityInitial();
}

class AvailabilityLoading extends AvailabilityState {
  const AvailabilityLoading();
}

class AvailabilityLoaded extends AvailabilityState {
  final DoctorAvailability? doctorAvailability;
  final List<TimeSlot> availableTimeSlots;
  final List<Appointment> appointments;
  final String? selectedTimeSlotId;
  final AppointmentFormData formData;

  const AvailabilityLoaded({
    this.doctorAvailability,
    this.availableTimeSlots = const [],
    this.appointments = const [],
    this.selectedTimeSlotId,
    this.formData = const AppointmentFormData(),
  });

  @override
  List<Object?> get props => [
    doctorAvailability,
    availableTimeSlots,
    appointments,
    selectedTimeSlotId,
    formData,
  ];

  AvailabilityLoaded copyWith({
    DoctorAvailability? doctorAvailability,
    List<TimeSlot>? availableTimeSlots,
    List<Appointment>? appointments,
    String? selectedTimeSlotId,
    AppointmentFormData? formData,
    bool clearSelectedTimeSlot = false,
  }) {
    return AvailabilityLoaded(
      doctorAvailability: doctorAvailability ?? this.doctorAvailability,
      availableTimeSlots: availableTimeSlots ?? this.availableTimeSlots,
      appointments: appointments ?? this.appointments,
      selectedTimeSlotId:
          clearSelectedTimeSlot
              ? null
              : (selectedTimeSlotId ?? this.selectedTimeSlotId),
      formData: formData ?? this.formData,
    );
  }
}

class AppointmentCreating extends AvailabilityState {
  const AppointmentCreating();
}

class AppointmentCreated extends AvailabilityState {
  final Appointment appointment;

  const AppointmentCreated({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

class AvailabilityError extends AvailabilityState {
  final String message;

  const AvailabilityError({required this.message});

  @override
  List<Object?> get props => [message];
}

class AppointmentUpdated extends AvailabilityState {
  final Appointment appointment;

  const AppointmentUpdated({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

class AppointmentCancelled extends AvailabilityState {
  final String appointmentId;

  const AppointmentCancelled({required this.appointmentId});

  @override
  List<Object?> get props => [appointmentId];
}

/// State when saving availability schedule
class AvailabilitySaving extends AvailabilityState {
  const AvailabilitySaving();

  @override
  List<Object?> get props => [];
}

/// State when availability schedule is saved successfully
class AvailabilitySaved extends AvailabilityState {
  final DoctorAvailabilitySchedule schedule;

  const AvailabilitySaved({required this.schedule});

  @override
  List<Object?> get props => [schedule];
}

// Helper class for form data
class AppointmentFormData extends Equatable {
  final String? doctorId;
  final String? patientName;
  final int? age;
  final String? appointmentType;
  final String? complaintType;
  final String? reason;

  const AppointmentFormData({
    this.doctorId,
    this.patientName,
    this.age,
    this.appointmentType,
    this.complaintType,
    this.reason,
  });

  @override
  List<Object?> get props => [
    doctorId,
    patientName,
    age,
    appointmentType,
    complaintType,
    reason,
  ];

  AppointmentFormData copyWith({
    String? doctorId,
    String? patientName,
    int? age,
    String? appointmentType,
    String? complaintType,
    String? reason,
  }) {
    return AppointmentFormData(
      doctorId: doctorId ?? this.doctorId,
      patientName: patientName ?? this.patientName,
      age: age ?? this.age,
      appointmentType: appointmentType ?? this.appointmentType,
      complaintType: complaintType ?? this.complaintType,
      reason: reason ?? this.reason,
    );
  }

  bool get isValid {
    return doctorId != null &&
        doctorId!.isNotEmpty &&
        patientName != null &&
        patientName!.isNotEmpty &&
        age != null &&
        age! > 0 &&
        appointmentType != null &&
        appointmentType!.isNotEmpty &&
        complaintType != null &&
        complaintType!.isNotEmpty &&
        reason != null &&
        reason!.isNotEmpty;
  }
}
