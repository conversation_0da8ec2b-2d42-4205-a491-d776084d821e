import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_details_by_id/get_appointments_details_by_id_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_details_by_id/get_appointments_details_by_id_response_model.dart';

abstract class GetAppointmentDetailsByIDRemoteDatasource {
  Future<GetAppointmentDetailsByIDResponseModel> getAppointmentDetailsById(
    GetAppointmentsDetailsByIDRequestModel request,
  );
}

class GetAppointmentDetailsByIDRemoteDatasourceImpl
    implements GetAppointmentDetailsByIDRemoteDatasource {
  final DioClient dioClient;

  GetAppointmentDetailsByIDRemoteDatasourceImpl({required this.dioClient});

  @override
  Future<GetAppointmentDetailsByIDResponseModel> getAppointmentDetailsById(
    GetAppointmentsDetailsByIDRequestModel request,
  ) async {
    try {
      // final queryParams = request.toJson();

      // final response = await dioClient.get(
      //   '/doctors/${request.appointmentID}/get-appointment-details',
      // );
      // return GetAppointmentsDetailByIDResponseModel.fromJson(response.data);

      await Future.delayed(const Duration(seconds: 2));

      final responseJson = {
        "data": {
          "appointment_id": "fc07cec1-6af4-46b3-84c3-c95586c1ef6d",
          "patient_name": "John Doe",
          "age": 43,
          "patient_phone": "555-020212",
          "gender": "male",
          "appointment_type": "virtual",
          "complaints": [
            {"label": "Back pain", "value": "complaint-uuid"},
          ],
          "status": "requested",
          "reason": "Recurring lower back pain for 2 weeks",
          "date": "2025-08-12",
          "start_time": "10:31:00",
          "end_time": "11:00:00",
          "files": [
            {
              "file_name":
                  "8f0cdb13-6f08-4f54-a5ce-b3182ac04831-Website Development Quote.pdf",
            },
          ],
        },
        "message": "Appointments details fetched successfully",
        "status": "success",
        "statusCode": 200,
      };

      return GetAppointmentDetailsByIDResponseModel.fromJson(responseJson);
    } catch (e) {
      throw ServerException();
    }
  }
}
