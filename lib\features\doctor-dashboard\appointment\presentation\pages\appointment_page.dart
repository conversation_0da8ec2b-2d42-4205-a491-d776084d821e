import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/services.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/di/injection_container.dart';
import '../../../../../core/router/app_router.dart';
import '../../../../../core/widgets/searchbar.dart';
import '../../domain/entities/appointment_entity.dart';
import '../bloc/appointment_bloc.dart';

class AppointmentsPage extends StatelessWidget {
  const AppointmentsPage({super.key});

  void _navigateToAppointmentDetail(BuildContext context, String appointmentId) {
    print('🔄 Navigating to appointment details with ID: $appointmentId');
    context.go('/appointment-details/$appointmentId');
  }

  void _navigateToNewAppointment(BuildContext context) {
    context.go(AppRouter.newAppointment);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<AppointmentBloc>()..add(LoadAppointmentsEvent()),
      child: Scaffold(
        body: BlocConsumer<AppointmentBloc, AppointmentState>(
          listener: (context, state) {
            if (state is AppointmentError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is AppointmentOperationSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
            } else if (state is NewAppointmentSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Appointment created successfully'),
                  backgroundColor: Colors.green,
                ),
              );
              // Optionally reload appointments after creating new one
              context.read<AppointmentBloc>().add(LoadAppointmentsEvent());
            } else if (state is NewAppointmentFailure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          builder: (context, state) {
            return _AppointmentsContent(
              state: state,
              onNavigateToDetail: (context, appointmentId) => _navigateToAppointmentDetail(context, appointmentId),
              onNavigateToNew: _navigateToNewAppointment,
              onCancelAppointment: (appointmentId) {
                context.read<AppointmentBloc>().add(CancelAppointmentEvent(appointmentId));
              },
              onConfirmAppointment: (appointmentId) {
                context.read<AppointmentBloc>().add(ConfirmAppointmentEvent(appointmentId));
              },
              onRefresh: () {
                context.read<AppointmentBloc>().add(RefreshAppointmentsEvent());
              },
            );
          },
        ),
      ),
    );
  }
}


class _AppointmentsContent extends StatelessWidget {
  final AppointmentState state;
  final Function(BuildContext, String) onNavigateToDetail;
  final Function(BuildContext) onNavigateToNew;
  final Function(String) onCancelAppointment;
  final Function(String) onConfirmAppointment;
  final VoidCallback onRefresh;

  const _AppointmentsContent({
    required this.state,
    required this.onNavigateToDetail,
    required this.onNavigateToNew,
    required this.onCancelAppointment,
    required this.onConfirmAppointment,
    required this.onRefresh,
  });

  void _showFullIdDialog(BuildContext context, String id) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Appointment ID'),
          content: SelectableText(
            id,
            style: const TextStyle(fontSize: 16, fontFamily: 'monospace'),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Close'),
            ),
            TextButton(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: id));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('ID copied to clipboard')),
                );
                Navigator.of(context).pop();
              },
              child: const Text('Copy'),
            ),
          ],
        );
      },
    );
  }

  String _truncateId(String id) {
    if (id.length <= 10) {
      return id;
    }
    return '${id.substring(0, 8)}...';
  }

  String _truncateText(String text, {int maxLength = 15}) {
    if (text.length <= maxLength) {
      return text;
    }
    return '${text.substring(0, maxLength)}...';
  }

  String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  @override
  Widget build(BuildContext context) {
    final bool isMobile = MediaQuery.of(context).size.width < 600;
    final bool isTablet = MediaQuery.of(context).size.width < 1024;
    final double horizontalPadding = isMobile
        ? AppConstants.paddingSmall
        : isTablet
        ? AppConstants.paddingMedium
        : AppConstants.paddingLarge;

    const OutlineInputBorder dropdownBorder = OutlineInputBorder(
      borderRadius:
      BorderRadius.all(Radius.circular(AppConstants.borderRadiusSmall)),
      borderSide: BorderSide(color: AppConstants.borderColor, width: 1.0),
    );

    if (state is AppointmentLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is AppointmentError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text((state as AppointmentError).message),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRefresh,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // Handle new appointment states
    if (state is NewAppointmentLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final List<AppointmentEntity> appointments = state is AppointmentLoaded
        ? (state as AppointmentLoaded).appointments
        : state is AppointmentOperationLoading
        ? (state as AppointmentOperationLoading).currentAppointments
        : state is AppointmentOperationSuccess
        ? (state as AppointmentOperationSuccess).appointments
        : [];

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding, vertical: AppConstants.paddingMedium),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 506,
                height: 40,
                child: CustomSearchBar(
                  hintText: "Search",
                  backgroundColor: Color.fromRGBO(34, 96, 255, 0.6),
                  hintColor: Colors.white,
                  iconColor: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
              ),
              if (isMobile) ...[
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [

                    const Text(
                      'Appointments',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppConstants.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    _buildDateNavigationMobile(context),
                    const SizedBox(height: AppConstants.paddingMedium),
                    _buildNewAppointmentButton(context, onNavigateToNew),
                  ],
                ),
              ] else ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Appointments',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: AppConstants.textPrimaryColor,
                      ),
                    ),
                    Flexible(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Flexible(child: _buildDateNavigation(context)),
                          if (!isTablet) const SizedBox(width: AppConstants.paddingMedium),
                          if (!isTablet) _buildNewAppointmentButton(context, onNavigateToNew),
                        ],
                      ),
                    ),
                  ],
                ),
                if (isTablet) ...[
                  const SizedBox(height: AppConstants.paddingMedium),
                  _buildNewAppointmentButton(context, onNavigateToNew),
                ],
              ],
              const SizedBox(height: AppConstants.sizedBoxHeightMedium),
              if (isMobile) ...[
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          'Show',
                          style: TextStyle(
                            fontSize: AppConstants.fontSize14,
                            color: AppConstants.textMidColor,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 70,
                          height: 36,
                          decoration: BoxDecoration(
                            border: Border.all(color: AppConstants.borderColor),
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                          ),
                          child: const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('100',
                                    style: TextStyle(fontSize: AppConstants.fontSize14)),
                                Icon(Icons.keyboard_arrow_down,
                                    size: 16,
                                    color: AppConstants.textMidColor),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'entries',
                          style: TextStyle(
                            fontSize: AppConstants.fontSize14,
                            color: AppConstants.textMidColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    Wrap(
                      spacing: AppConstants.paddingSmall,
                      runSpacing: AppConstants.paddingSmall,
                      children: [
                        _buildFilterDropdown('All Dates', dropdownBorder),
                        _buildFilterDropdown('Type', dropdownBorder),
                        _buildFilterDropdown('Status', dropdownBorder),
                      ],
                    ),
                  ],
                ),
              ] else if (isTablet) ...[
                Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Row(
                            children: [
                              const Text(
                                'Show',
                                style: TextStyle(
                                  fontSize: AppConstants.fontSize14,
                                  color: AppConstants.textMidColor,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                width: 70,
                                height: 36,
                                decoration: BoxDecoration(
                                  border: Border.all(color: AppConstants.borderColor),
                                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                                ),
                                child: const Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text('100',
                                          style: TextStyle(fontSize: AppConstants.fontSize14)),
                                      Icon(Icons.keyboard_arrow_down,
                                          size: 16,
                                          color: AppConstants.textMidColor),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'entries',
                                style: TextStyle(
                                  fontSize: AppConstants.fontSize14,
                                  color: AppConstants.textMidColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Export Button for tablet
                        OutlinedButton.icon(
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppConstants.primaryColor,
                            backgroundColor: AppConstants.backgroundColor,
                            side: const BorderSide(color: AppConstants.borderColor2),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          onPressed: () {},
                          icon: const Icon(Icons.file_download_outlined, size: 16),
                          label: const Text('Export', style: TextStyle(fontSize: AppConstants.fontSize14)),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    // Filters for tablet
                    Wrap(
                      spacing: AppConstants.paddingSmall,
                      children: [
                        _buildFilterDropdown('All Dates', dropdownBorder),
                        _buildFilterDropdown('Type', dropdownBorder),
                        _buildFilterDropdown('Status', dropdownBorder),
                      ],
                    ),
                  ],
                ),
              ] else ...[
                // Desktop layout
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Row(
                        children: [
                          const Text(
                            'Show',
                            style: TextStyle(
                              fontSize: AppConstants.fontSize14,
                              color: AppConstants.textMidColor,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            width: 70,
                            height: 36,
                            decoration: BoxDecoration(
                              border: Border.all(color: AppConstants.borderColor),
                              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 8.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text('100',
                                      style: TextStyle(fontSize: AppConstants.fontSize14)),
                                  Icon(Icons.keyboard_arrow_down,
                                      size: 16,
                                      color: AppConstants.textMidColor),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'entries',
                            style: TextStyle(
                              fontSize: AppConstants.fontSize14,
                              color: AppConstants.textMidColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Right Side - Filters and Export
                    Flexible(
                      child: Wrap(
                        alignment: WrapAlignment.end,
                        spacing: AppConstants.paddingSmall,
                        children: [
                          _buildFilterDropdown('All Dates', dropdownBorder),
                          _buildFilterDropdown('Type', dropdownBorder),
                          _buildFilterDropdown('Status', dropdownBorder),
                          const SizedBox(width: AppConstants.paddingSmall),
                          OutlinedButton.icon(
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppConstants.primaryColor,
                              backgroundColor: AppConstants.backgroundColor,
                              side: const BorderSide(color: AppConstants.borderColor2),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            onPressed: () {},
                            icon: const Icon(Icons.file_download_outlined, size: 16),
                            label: const Text('Export', style: TextStyle(fontSize: AppConstants.fontSize14)),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: AppConstants.sizedBoxHeightLarge),

              // Data Table - Responsive
              Expanded(
                child: _buildResponsiveAppointmentsList(
                  context,
                  appointments,
                  onNavigateToDetail,
                  onCancelAppointment,
                  onConfirmAppointment,
                ),
              ),

              const SizedBox(height: AppConstants.sizedBoxHeightMedium),

              // Pagination - Responsive
              _buildPagination(context, isMobile),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDateNavigation(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.borderColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 16, color: AppConstants.textHighColor),
            onPressed: () {},
            padding: const EdgeInsets.all(8),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              'Today',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textHighColor,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios, size: 16, color: AppConstants.textHighColor),
            onPressed: () {},
            padding: const EdgeInsets.all(8),
          ),
        ],
      ),
    );
  }

  Widget _buildDateNavigationMobile(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.borderColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 16, color: AppConstants.textHighColor),
            onPressed: () {},
            padding: const EdgeInsets.all(12),
          ),
          const Expanded(
            child: Center(
              child: Text(
                'Today',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  color: AppConstants.textHighColor,
                ),
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios, size: 16, color: AppConstants.textHighColor),
            onPressed: () {},
            padding: const EdgeInsets.all(12),
          ),
        ],
      ),
    );
  }

  Widget _buildNewAppointmentButton(BuildContext context, Function(BuildContext) onNavigateToNew) {
    final bool isMobile = MediaQuery.of(context).size.width < 600;

    return SizedBox(
      width: isMobile ? double.infinity : 200,
      height: AppConstants.buttonHeight,
      child: ElevatedButton.icon(
        onPressed: () => onNavigateToNew(context),
        icon: const Icon(Icons.add, size: 20, color: AppConstants.elevatedButtonForegroundColor),
        label: Text(
          'New Appointment',
          style: TextStyle(
            fontSize: isMobile ? AppConstants.fontSize14 : AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
            color: AppConstants.elevatedButtonForegroundColor,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveAppointmentsList(
      BuildContext context,
      List<AppointmentEntity> appointments,
      Function(BuildContext, String) onNavigateToDetail,
      Function(String) onCancelAppointment,
      Function(String) onConfirmAppointment,
      ) {
    final bool isMobile = MediaQuery.of(context).size.width < 600;
    final bool isTablet = MediaQuery.of(context).size.width < 1024;

    if (isMobile) {
      return _buildMobileAppointmentsList(
        context,
        appointments,
        onNavigateToDetail,
        onCancelAppointment,
        onConfirmAppointment,
      );
    } else if (isTablet) {
      return _buildTabletAppointmentsTable(
        context,
        appointments,
        onNavigateToDetail,
        onCancelAppointment,
        onConfirmAppointment,
      );
    } else {
      return _buildDesktopAppointmentsTable(
        context,
        appointments,
        onNavigateToDetail,
        onCancelAppointment,
        onConfirmAppointment,
      );
    }
  }

  Widget _buildMobileAppointmentsList(
      BuildContext context,
      List<AppointmentEntity> appointments,
      Function(BuildContext, String) onNavigateToDetail,
      Function(String) onCancelAppointment,
      Function(String) onConfirmAppointment,
      ) {
    if (appointments.isEmpty) {
      return const Center(child: Text('No appointments found'));
    }

    return ListView.separated(
      itemCount: appointments.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppConstants.paddingSmall),
      itemBuilder: (context, index) {
        final appointment = appointments[index];
        return _buildAppointmentCard(
          context,
          appointment,
          onNavigateToDetail,
          onCancelAppointment,
          onConfirmAppointment,
          isMobile: true,
        );
      },
    );
  }

  Widget _buildTabletAppointmentsTable(
      BuildContext context,
      List<AppointmentEntity> appointments,
      Function(BuildContext, String) onNavigateToDetail,
      Function(String) onCancelAppointment,
      Function(String) onConfirmAppointment,
      ) {
    return _buildAppointmentsTable(
      context,
      appointments,
      onNavigateToDetail,
      onCancelAppointment,
      onConfirmAppointment,
      showAllColumns: false,
      isTablet: true,
    );
  }

  Widget _buildDesktopAppointmentsTable(
      BuildContext context,
      List<AppointmentEntity> appointments,
      Function(BuildContext, String) onNavigateToDetail,
      Function(String) onCancelAppointment,
      Function(String) onConfirmAppointment,
      ) {
    return _buildAppointmentsTable(
      context,
      appointments,
      onNavigateToDetail,
      onCancelAppointment,
      onConfirmAppointment,
      showAllColumns: true,
      isTablet: false,
    );
  }

  Widget _buildAppointmentsTable(
      BuildContext context,
      List<AppointmentEntity> appointments,
      Function(BuildContext, String) onNavigateToDetail,
      Function(String) onCancelAppointment,
      Function(String) onConfirmAppointment, {
        required bool showAllColumns,
        bool isTablet = false,
      }) {
    if (appointments.isEmpty) {
      return const Center(child: Text('No appointments found'));
    }

    // Define optimized column widths
    final Map<String, double> columnWidths = {
      'TIME': isTablet ? 70.0 : 80.0,
      'PATIENT': isTablet ? 90.0 : 110.0,
      'ID': 80.0,
      'GENDER': 70.0,
      'PHONE': isTablet ? 90.0 : 100.0,
      'STATUS': isTablet ? 90.0 : 100.0,
      'ACTION': isTablet ? 130.0 : 150.0,
    };

    final List<DataColumn> columns = [
      DataColumn(
        label: SizedBox(
          width: columnWidths['TIME'],
          child: const Text('TIME', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        tooltip: 'Appointment Time',
      ),
      DataColumn(
        label: SizedBox(
          width: columnWidths['PATIENT'],
          child: const Text('PATIENT', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        tooltip: 'Patient Name',
      ),
      if (showAllColumns)
        DataColumn(
          label: SizedBox(
            width: columnWidths['ID'],
            child: const Text('ID', style: TextStyle(fontWeight: FontWeight.bold)),
          ),
          tooltip: 'Appointment ID',
        ),
      if (showAllColumns)
        DataColumn(
          label: SizedBox(
            width: columnWidths['GENDER'],
            child: const Text('GENDER', style: TextStyle(fontWeight: FontWeight.bold)),
          ),
          tooltip: 'Patient Gender',
        ),
      DataColumn(
        label: SizedBox(
          width: columnWidths['PHONE'],
          child: const Text('PHONE', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        tooltip: 'Phone Number',
      ),
      DataColumn(
        label: SizedBox(
          width: columnWidths['STATUS'],
          child: const Text('STATUS', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        tooltip: 'Appointment Status',
      ),
      DataColumn(
        label: SizedBox(
          width: columnWidths['ACTION'],
          child: const Text('ACTION', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        tooltip: 'Available Actions',
      ),
    ];

    return Container(
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: AppConstants.borderColor),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minWidth: (MediaQuery.of(context).size.width*0.95)- (AppConstants.paddingLarge*2),

          ),
          child: DataTable(
            columnSpacing: 8, // Reduced from 10
            horizontalMargin: 50, // Reduced from previous value
            headingRowHeight: 48,
            dataRowHeight: 40,
            headingRowColor: WidgetStateProperty.all(AppConstants.inputFieldBackgroundColor),
            columns: columns,
            rows: appointments.map((appointment) {
              return DataRow(cells: [
                DataCell(
                  SizedBox(
                    width: columnWidths['TIME'],
                    child: Text(
                      _truncateText(appointment.time, maxLength: 8),
                      style: const TextStyle(fontSize: AppConstants.fontSize14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(
                  SizedBox(
                    width: columnWidths['PATIENT'],
                    child: Text(
                      _truncateText(appointment.patientName, maxLength: isTablet ? 12 : 15),
                      style: const TextStyle(fontSize: AppConstants.fontSize14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                if (showAllColumns)
                  DataCell(
                    SizedBox(
                      width: columnWidths['ID'],
                      child: _buildTruncatedId(context, appointment.bookingId),
                    ),
                  ),
                if (showAllColumns)
                  DataCell(
                    SizedBox(
                      width: columnWidths['GENDER'],
                      child: Text(
                        _capitalize(appointment.gender),
                        style: const TextStyle(fontSize: AppConstants.fontSize14),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                DataCell(
                  SizedBox(
                    width: columnWidths['PHONE'],
                    child: Text(
                      _truncateText(appointment.phone, maxLength: 10),
                      style: const TextStyle(fontSize: AppConstants.fontSize14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(
                  SizedBox(
                    width: columnWidths['STATUS'],
                    child: _buildStatusWidget(appointment.status),
                  ),
                ),
                DataCell(
                  SizedBox(
                    width: columnWidths['ACTION'],
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.cancel_outlined, size: 18, color: AppConstants.errorColor),
                          tooltip: 'Cancel',
                          onPressed: () => onCancelAppointment(appointment.appointmentId),
                          padding: const EdgeInsets.all(4),
                        ),
                        IconButton(
                          icon: const Icon(Icons.check_circle_outline, size: 18, color: Colors.green),
                          tooltip: 'Confirm',
                          onPressed: () => onConfirmAppointment(appointment.appointmentId),
                          padding: const EdgeInsets.all(4),
                        ),
                        const SizedBox(width: 2),
                        ElevatedButton(
                          onPressed: () => onNavigateToDetail(context, appointment.appointmentId),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConstants.inputFieldBackgroundColor2,
                            foregroundColor: AppConstants.textPrimaryColor,
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            minimumSize: const Size(0, 28),
                            visualDensity: VisualDensity.compact,
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.remove_red_eye_outlined, size: 14, color: AppConstants.primaryColor),
                              SizedBox(width: 2),
                              Text('View', style: TextStyle(fontSize: 12)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ]);
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildTruncatedId(BuildContext context, String id) {
    return Tooltip(
      message: id,
      child: GestureDetector(
        onTap: () {
          _showFullIdDialog(context, id);
        },
        child: Text(
          _truncateId(id),
          style: const TextStyle(
            fontSize: AppConstants.fontSize14,
            overflow: TextOverflow.ellipsis,
          ),
          maxLines: 1,
        ),
      ),
    );
  }

  Widget _buildAppointmentCard(
      BuildContext context,
      AppointmentEntity appointment,
      Function(BuildContext, String) onNavigateToDetail,
      Function(String) onCancelAppointment,
      Function(String) onConfirmAppointment, {
        required bool isMobile,
      }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    appointment.time,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: AppConstants.fontSizeMedium,
                      color: AppConstants.textPrimaryColor,
                    ),
                  ),
                ),
                _buildStatusWidget(appointment.status),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Name', appointment.patientName),
            _buildInfoRowWithTruncatedId(context, 'ID', appointment.bookingId),
            _buildInfoRow('Gender', _capitalize(appointment.gender)),
            _buildInfoRow('Phone', appointment.phone),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.cancel_outlined, size: 20, color: AppConstants.errorColor),
                      tooltip: 'Cancel',
                      onPressed: () => onCancelAppointment(appointment.appointmentId),
                    ),
                    IconButton(
                      icon: const Icon(Icons.check_circle_outline, size: 20, color: Colors.green),
                      tooltip: 'Confirm',
                      onPressed: () => onConfirmAppointment(appointment.appointmentId),
                    ),
                  ],
                ),
                ElevatedButton(
                  onPressed: () => onNavigateToDetail(context, appointment.appointmentId),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.inputFieldBackgroundColor2,
                    foregroundColor: AppConstants.textPrimaryColor,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    minimumSize: const Size(0, 36),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.remove_red_eye_outlined, size: 16, color: AppConstants.primaryColor),
                      SizedBox(width: 4),
                      Text('View', style: TextStyle(fontSize: AppConstants.fontSize14)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: AppConstants.fontSize14,
                color: AppConstants.textMidColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: AppConstants.fontSize14,
                color: AppConstants.textPrimaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowWithTruncatedId(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: AppConstants.fontSize14,
                color: AppConstants.textMidColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                _showFullIdDialog(context, value);
              },
              child: Tooltip(
                message: value,
                child: Text(
                  _truncateId(value),
                  style: const TextStyle(
                    fontSize: AppConstants.fontSize14,
                    color: AppConstants.textPrimaryColor,
                    overflow: TextOverflow.ellipsis,
                  ),
                  maxLines: 1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusWidget(String status) {
    final statusLower = status.toLowerCase();
    IconData icon;
    Color color;

    switch (statusLower) {
      case 'visited':
        icon = Icons.check_circle;
        color = Colors.green;
        break;
      case 'confirmed':
        icon = Icons.verified;
        color = Colors.blue;
        break;
      case 'scheduled':
      default:
        icon = Icons.schedule;
        color = Colors.orange;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            _capitalize(status),
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(String text, OutlineInputBorder border) {
    return Container(
      height: 36,
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.borderColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              text,
              style: const TextStyle(
                fontSize: AppConstants.fontSize14,
                color: AppConstants.textHighColor,
              ),
            ),
            const SizedBox(width: 6),
            const Icon(
              Icons.keyboard_arrow_down,
              size: 16,
              color: AppConstants.textMidColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPagination(BuildContext context, bool isMobile) {
    if (isMobile) {
      return Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back_ios, size: 16, color: AppConstants.textHighColor),
              onPressed: () {},
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            const Text(
              '1 of 40',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textHighColor,
              ),
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            IconButton(
              icon: const Icon(Icons.arrow_forward_ios, size: 16, color: AppConstants.textHighColor),
              onPressed: () {},
            ),
          ],
        ),
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 16, color: AppConstants.textHighColor),
            onPressed: () {},
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          const Text(
            '1 of 40',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textHighColor,
            ),
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios, size: 16, color: AppConstants.textHighColor),
            onPressed: () {},
          ),
        ],
      );
    }
  }
}