import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_data.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_response.dart';

class UpdateAppointmentStatusByAppointmentidResponseModel
    extends UpdateAppointmentStatusByAppointmentidResponse {
  const UpdateAppointmentStatusByAppointmentidResponseModel({
    required super.data,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory UpdateAppointmentStatusByAppointmentidResponseModel.fromJson(
    Map<String, dynamic> json,
  ) {
    return UpdateAppointmentStatusByAppointmentidResponseModel(
      data: UpdateAppointmentStatusByAppointmentidData(
        id: json['data']['id'] as String,
      ),

      message: json['message'] as String,
      status: json['status'] as String,
      statusCode: json['statusCode'] as int,
    );
  }
}
