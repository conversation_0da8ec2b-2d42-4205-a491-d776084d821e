import 'package:dio/dio.dart';
import 'package:imed_fe/core/config/env_config.dart';
import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import '../models/appointment_model.dart';
import '../models/doctor_availability_model.dart';
import '../models/doctor_availability_schedule_model.dart';
import '../models/time_slot_model.dart';

abstract class AvailabilityRemoteDataSource {
  Future<AppointmentModel> createAppointment(AppointmentModel appointment);
  Future<DoctorAvailabilityModel> getDoctorAvailability({
    required String doctorId,
    required DateTime date,
  });
  Future<List<TimeSlotModel>> getAvailableTimeSlots({
    required String doctorId,
    required DateTime date,
  });
  Future<List<AppointmentModel>> getPatientAppointments({
    required String patientId,
  });
  Future<List<AppointmentModel>> getDoctorAppointments({
    required String doctorId,
    DateTime? date,
  });
  Future<AppointmentModel> updateAppointmentStatus({
    required String appointmentId,
    required String status,
  });
  Future<bool> cancelAppointment({required String appointmentId});

  /// Sets doctor availability schedule
  Future<DoctorAvailabilityScheduleModel> setDoctorAvailability(
    DoctorAvailabilityScheduleModel schedule,
  );
}

class AvailabilityRemoteDataSourceImpl implements AvailabilityRemoteDataSource {
  final DioClient dioClient;

  AvailabilityRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<AppointmentModel> createAppointment(
    AppointmentModel appointment,
  ) async {
    try {
      // Create FormData for the appointment
      final formData = FormData.fromMap(appointment.toJson());

      final response = await dioClient.post(
        EnvConfig.appointmentsEndpoint,
        data: formData,
        options: Options(contentType: 'multipart/form-data'),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return AppointmentModel.fromJson(response.data);
      } else {
        throw ServerException(
          message: 'Failed to create appointment',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ServerException(
          message: e.response?.data['message'] ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw NetworkException(message: 'Network error occurred');
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  @override
  Future<DoctorAvailabilityModel> getDoctorAvailability({
    required String doctorId,
    required DateTime date,
  }) async {
    try {
      print('🔄 API Call: GET ${EnvConfig.availabilityEndpoint}');
      print('📅 Date: ${date.toIso8601String().split('T')[0]}');

      final response = await dioClient.get(
        EnvConfig.availabilityEndpoint,
        queryParameters: {
          'date': date.toIso8601String().split('T')[0], // YYYY-MM-DD format
        },
      );

      print('✅ API Response Status: ${response.statusCode}');
      print('📦 API Response Data: ${response.data}');

      if (response.statusCode == 200) {
        return DoctorAvailabilityModel.fromJson(response.data);
      } else {
        print('❌ API Error: Failed to get doctor availability');
        throw ServerException(
          message: 'Failed to get doctor availability',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      print('❌ DioException: ${e.message}');
      print('❌ Response Data: ${e.response?.data}');
      if (e.response != null) {
        throw ServerException(
          message: e.response?.data['message'] ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw NetworkException(message: 'Network error occurred');
      }
    } catch (e) {
      print('❌ Unexpected Error: $e');
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  @override
  Future<List<TimeSlotModel>> getAvailableTimeSlots({
    required String doctorId,
    required DateTime date,
  }) async {
    try {
      final response = await dioClient.get(
        '${EnvConfig.availableSlotTimesEndpoint}/$doctorId',
        queryParameters: {
          'date': date.toIso8601String().split('T')[0],
          'available_only': true,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['time_slots'] ?? response.data;
        return data.map((slot) => TimeSlotModel.fromJson(slot)).toList();
      } else {
        throw ServerException(
          message: 'Failed to get available time slots',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ServerException(
          message: e.response?.data['message'] ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw NetworkException(message: 'Network error occurred');
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  @override
  Future<List<AppointmentModel>> getPatientAppointments({
    required String patientId,
  }) async {
    try {
      final response = await dioClient.get(
        '${EnvConfig.patientsEndpoint}/$patientId/appointments',
      );

      if (response.statusCode == 200) {
        final List<dynamic> data =
            response.data['appointments'] ?? response.data;
        return data
            .map((appointment) => AppointmentModel.fromJson(appointment))
            .toList();
      } else {
        throw ServerException(
          message: 'Failed to get patient appointments',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ServerException(
          message: e.response?.data['message'] ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw NetworkException(message: 'Network error occurred');
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  @override
  Future<List<AppointmentModel>> getDoctorAppointments({
    required String doctorId,
    DateTime? date,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (date != null) {
        queryParams['date'] = date.toIso8601String().split('T')[0];
      }

      final response = await dioClient.get(
        '${EnvConfig.appointmentsEndpoint}/$doctorId',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data =
            response.data['appointments'] ?? response.data;
        return data
            .map((appointment) => AppointmentModel.fromJson(appointment))
            .toList();
      } else {
        throw ServerException(
          message: 'Failed to get doctor appointments',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ServerException(
          message: e.response?.data['message'] ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw NetworkException(message: 'Network error occurred');
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  @override
  Future<AppointmentModel> updateAppointmentStatus({
    required String appointmentId,
    required String status,
  }) async {
    try {
      final response = await dioClient.post(
        '${EnvConfig.appointmentsEndpoint}/$appointmentId/status',
        data: {'status': status},
      );

      if (response.statusCode == 200) {
        return AppointmentModel.fromJson(response.data);
      } else {
        throw ServerException(
          message: 'Failed to update appointment status',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ServerException(
          message: e.response?.data['message'] ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw NetworkException(message: 'Network error occurred');
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  @override
  Future<bool> cancelAppointment({required String appointmentId}) async {
    try {
      final response = await dioClient.post(
        '${EnvConfig.appointmentsEndpoint}/$appointmentId/cancel',
      );

      if (response.statusCode == 200) {
        return response.data['success'] ?? true;
      } else {
        throw ServerException(
          message: 'Failed to cancel appointment',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw ServerException(
          message: e.response?.data['message'] ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw NetworkException(message: 'Network error occurred');
      }
    } catch (e) {
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  @override
  Future<DoctorAvailabilityScheduleModel> setDoctorAvailability(
    DoctorAvailabilityScheduleModel schedule,
  ) async {
    try {
      print('🔄 API Call: POST ${EnvConfig.availabilityEndpoint}');
      print('📦 Request Data: ${schedule.toJson()}');

      final response = await dioClient.post(
        EnvConfig.availabilityEndpoint,
        data: schedule.toJson(),
      );

      print('✅ API Response Status: ${response.statusCode}');
      print('📦 API Response Data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return DoctorAvailabilityScheduleModel.fromJson(response.data);
      } else {
        print('❌ API Error: Failed to set doctor availability');
        throw ServerException(
          message: 'Failed to set doctor availability',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      print('❌ DioException: ${e.message}');
      print('❌ Response Data: ${e.response?.data}');
      if (e.response != null) {
        throw ServerException(
          message: e.response?.data['message'] ?? 'Server error occurred',
          statusCode: e.response?.statusCode,
        );
      } else {
        throw NetworkException(message: 'Network error occurred');
      }
    } catch (e) {
      print('❌ Unexpected Error: $e');
      throw ServerException(message: 'Unexpected error occurred');
    }
  }
}
