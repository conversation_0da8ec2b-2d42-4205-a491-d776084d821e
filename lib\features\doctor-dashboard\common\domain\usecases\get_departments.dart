import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_departments/get_departments_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_departments/get_departments_request.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_departments/get_departments_response.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_departments_repositories.dart';

class GetDepartments
    implements UseCase<GetDepartmentsResponse, GetDepartmentsRequest> {
  final GetDepartmentsRepositories repository;

  GetDepartments(this.repository);

  @override
  Future<Either<Failure, GetDepartmentsResponse>> call(
    GetDepartmentsRequest params,
  ) async {
    final requestModel = GetDepartmentsRequestModel.fromEntity(params);
    return await repository.getAllDepartments(requestModel);
  }
}
