import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/data/models/appointment/get_appointments_by_doctor/get_appointments_by_doctor_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/appointment/update_appointment_status_by_appointmentid/update_appointment_status_by_appointmentid_request.dart';

abstract class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object> get props => [];
}

class CallGetDashboardData extends DashboardEvent {
  const CallGetDashboardData();

  @override
  List<Object> get props => [];
}

class CallGetAppointmentRequestList extends DashboardEvent {
  final GetAppointmentsByDoctorRequestModel appointmentsByDoctorRequestModel;
  const CallGetAppointmentRequestList({
    required this.appointmentsByDoctorRequestModel,
  });

  @override
  List<Object> get props => [appointmentsByDoctorRequestModel];
}

class CallGetTodayAppointmentsList extends DashboardEvent {
  final GetAppointmentsByDoctorRequestModel appointmentsByDoctorRequestModel;
  const CallGetTodayAppointmentsList({
    required this.appointmentsByDoctorRequestModel,
  });

  @override
  List<Object> get props => [appointmentsByDoctorRequestModel];
}

class CallGetRecentPatientsList extends DashboardEvent {
  final GetAppointmentsByDoctorRequestModel appointmentsByDoctorRequestModel;
  const CallGetRecentPatientsList({
    required this.appointmentsByDoctorRequestModel,
  });

  @override
  List<Object> get props => [appointmentsByDoctorRequestModel];
}

class CallUpdateAppointmentStatusByAppointmentId extends DashboardEvent {
  final UpdateAppointmentStatusByAppointmentidRequest
  appointmentStatusByAppointmentidRequest;
  const CallUpdateAppointmentStatusByAppointmentId({
    required this.appointmentStatusByAppointmentidRequest,
  });

  @override
  List<Object> get props => [appointmentStatusByAppointmentidRequest];
}
