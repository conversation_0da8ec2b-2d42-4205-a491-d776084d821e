import 'package:equatable/equatable.dart';

class GetHospitalsData extends Equatable {
  final List<Hospital> data;

  const GetHospitalsData({required this.data});

  @override
  List<Object?> get props => [data];
}

class Hospital extends Equatable {
  final String value;
  final String label;

  const Hospital({required this.value, required this.label});

  @override
  List<Object?> get props => [value, label];
}
