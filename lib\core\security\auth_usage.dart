// QUICK START - Token Management with flutter_secure_storage
// ===========================================================

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:imed_fe/core/security/bloc/auth_bloc.dart';
import 'package:imed_fe/core/security/bloc/auth_event.dart';
import 'package:imed_fe/core/security/bloc/auth_state.dart';
import 'package:imed_fe/core/security/services/token_service.dart';
import 'package:imed_fe/core/security/storage/token_storage.dart';

final sl = GetIt.instance;

// 1. LOGIN - Save token after Azure login
void handleLogin(
  BuildContext context,
  Map<String, dynamic> azureTokenResponse,
) {
  // azureTokenResponse from Azure AD:
  // {
  //   "access_token": "eyJ...",
  //   "refresh_token": "0.AR...",
  //   "expires_in": 3600,
  //   "token_type": "Bearer"
  // }

  final tokenModel = TokenModel.fromAzureResponse(azureTokenResponse);
  context.read<AuthBloc>().add(AuthLoginEvent(tokenModel));
}

// 2. CHECK AUTH on app startup
void checkAuthOnStartup(BuildContext context) {
  context.read<AuthBloc>().add(const AuthCheckStatusEvent());
}

// 3. USE API - Token handled automatically by AuthInterceptor
Future<void> getAppointments() async {
  final dioClient = sl<DioClient>();
  // No token handling needed! AuthInterceptor does:
  // - Check token expiry
  // - Auto-refresh if needed
  // - Add Authorization header
  // - Retry on 401 after refresh
  await dioClient.get('/appointments');
}

// 4. REFRESH TOKEN manually
void manualRefresh(BuildContext context) {
  context.read<AuthBloc>().add(const AuthRefreshTokenEvent());
}

// 5. PRINT TOKEN INFO to console
void printTokenInfo(BuildContext context) {
  context.read<AuthBloc>().add(const AuthPrintInfoEvent());

  // Output:
  // ╔════════════════════════════════════════════════════════╗
  // ║                    TOKEN INFO                          ║
  // ╠════════════════════════════════════════════════════════╣
  // ║ Access Token:   ✅ Present
  // ║ Refresh Token:  ✅ Present
  // ║ Expires At:     2024-12-02T15:30:45.123456
  // ║ Is Expired:     ✅ NO
  // ╚════════════════════════════════════════════════════════╝
}

// 6. LOGOUT
void logout(BuildContext context) {
  context.read<AuthBloc>().add(const AuthLogoutEvent());
}

// 7. LISTEN to auth state changes
class MyAuthListener extends StatelessWidget {
  const MyAuthListener({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          print('✅ User logged in - token: ${state.accessToken}');
          // Navigate to home
        } else if (state is AuthUnauthenticated) {
          print('❌ User logged out');
          // Navigate to login
        } else if (state is AuthError) {
          print('❌ Error: ${state.message}');
        }
      },
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          if (state is AuthLoading) return const CircularProgressIndicator();
          if (state is AuthAuthenticated) return const HomeScreen();
          return const LoginScreen();
        },
      ),
    );
  }
}

// 8. DIRECT ACCESS to tokens
Future<void> accessTokensDirectly() async {
  final tokenStorage = sl<TokenStorage>();

  final accessToken = await tokenStorage.getAccessToken();
  final refreshToken = await tokenStorage.getRefreshToken();
  final expiresAt = await tokenStorage.getTokenExpiresAt();
  final isExpired = await tokenStorage.isTokenExpired();

  print('Access: $accessToken');
  print('Refresh: $refreshToken');
  print('Expires: $expiresAt');
  print('Expired: $isExpired');
}

// 9. SETUP in injection_container.dart
// Already done! Just update these values:
// azureClientId: 'YOUR_CLIENT_ID',
// azureClientSecret: 'YOUR_CLIENT_SECRET',
// azureTokenEndpoint: 'YOUR_TENANT_ENDPOINT',

// 10. CONSOLE LOGS - View in DevTools
// [TokenStorage] ✅ Token saved
// [TokenManager] ⚠️ Token expired, refreshing...
// [TokenManager] 🔄 Refreshing token from Azure...
// [TokenManager] ✅ Token refreshed
// [AuthInterceptor] → GET /appointments
// [AuthInterceptor] ✅ Token added to header
// [AuthInterceptor] ← Response 200
// [AuthBloc] ✅ Login successful

// Placeholder classes
class DioClient {
  Future<dynamic> get(String path) => Future.value(null);
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Scaffold();
}

class LoginScreen extends StatelessWidget {
  const LoginScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Scaffold();
}
