import 'package:equatable/equatable.dart';
import 'package:imed_fe/core/security/services/token_service.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthLoginEvent extends AuthEvent {
  final TokenModel tokenModel;

  const AuthLoginEvent(this.tokenModel);

  @override
  List<Object?> get props => [tokenModel];
}

class AuthCheckStatusEvent extends AuthEvent {
  const AuthCheckStatusEvent();
}

class AuthRefreshTokenEvent extends AuthEvent {
  const AuthRefreshTokenEvent();
}

class AuthLogoutEvent extends AuthEvent {
  const AuthLogoutEvent();
}

class AuthPrintInfoEvent extends AuthEvent {
  const AuthPrintInfoEvent();
}
