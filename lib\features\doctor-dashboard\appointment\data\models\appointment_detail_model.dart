class AppointmentDetail {
  final String appointmentId;
  final String patientName;
  final int age;
  final String patientPhone;
  final String appointmentType;
  final List<Complaint> complaints;
  final String status;
  final String reason;
  final String date;
  final String startTime;
  final String endTime;
  final List<AppointmentFile> files;

  AppointmentDetail({
    required this.appointmentId,
    required this.patientName,
    required this.age,
    required this.patientPhone,
    required this.appointmentType,
    required this.complaints,
    required this.status,
    required this.reason,
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.files,
  });

  String get formattedDate {
    try {
      final dateTime = DateTime.parse(date);
      return '${_getWeekday(dateTime.weekday)}, ${dateTime.day} ${_getMonth(dateTime.month)} ${dateTime.year}';
    } catch (e) {
      return date; // Return original date if parsing fails
    }
  }

  String get formattedTime {
    try {
      final time = startTime.split(':');
      final hour = int.parse(time[0]);
      final minute = time[1];
      final period = hour >= 12 ? 'PM' : 'AM';
      final displayHour = hour > 12 ? hour - 12 : hour == 0 ? 12 : hour;
      return '${displayHour.toString().padLeft(2, '0')}:$minute $period';
    } catch (e) {
      return startTime; // Return original time if parsing fails
    }
  }

  String _getWeekday(int weekday) {
    const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return weekdays[weekday - 1];
  }

  String _getMonth(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }
}

class Complaint {
  final String label;
  final String value;

  Complaint({required this.label, required this.value});
}

class AppointmentFile {
  final String fileName;

  AppointmentFile({required this.fileName});
}

class AppointmentDetailModel {
  final AppointmentDetailData data;
  final String message;
  final String status;
  final int statusCode;

  AppointmentDetailModel({
    required this.data,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  factory AppointmentDetailModel.fromJson(Map<String, dynamic> json) {
    return AppointmentDetailModel(
      data: AppointmentDetailData.fromJson(json['data']),
      message: json['message'],
      status: json['status'],
      statusCode: json['statusCode'],
    );
  }

  // Convert model to domain entity
  AppointmentDetail toEntity() {
    return AppointmentDetail(
      appointmentId: data.appointmentId,
      patientName: data.patientName,
      age: data.age,
      patientPhone: data.patientPhone,
      appointmentType: data.appointmentType,
      complaints: data.complaints.map((complaint) => Complaint(
        label: complaint.label,
        value: complaint.value,
      )).toList(),
      status: data.status,
      reason: data.reason,
      date: data.date,
      startTime: data.startTime,
      endTime: data.endTime,
      files: data.files.map((file) => AppointmentFile(
        fileName: file.fileName,
      )).toList(),
    );
  }
}

class AppointmentDetailData {
  final String appointmentId;
  final String patientName;
  final int age;
  final String patientPhone;
  final String appointmentType;
  final List<ComplaintData> complaints;
  final String status;
  final String reason;
  final String date;
  final String startTime;
  final String endTime;
  final List<FileData> files;

  AppointmentDetailData({
    required this.appointmentId,
    required this.patientName,
    required this.age,
    required this.patientPhone,
    required this.appointmentType,
    required this.complaints,
    required this.status,
    required this.reason,
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.files,
  });

  factory AppointmentDetailData.fromJson(Map<String, dynamic> json) {
    return AppointmentDetailData(
      appointmentId: json['appointment_id'] ?? '',
      patientName: json['patient_name'] ?? '',
      age: json['age'] ?? 0,
      patientPhone: json['patient_phone'] ?? '',
      appointmentType: json['appointment_type'] ?? '',
      complaints: (json['complaints'] as List? ?? [])
          .map((complaint) => ComplaintData.fromJson(complaint))
          .toList(),
      status: json['status'] ?? '',
      reason: json['reason'] ?? '',
      date: json['date'] ?? '',
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
      files: (json['files'] as List? ?? [])
          .map((file) => FileData.fromJson(file))
          .toList(),
    );
  }
}

class ComplaintData {
  final String label;
  final String value;

  ComplaintData({required this.label, required this.value});

  factory ComplaintData.fromJson(Map<String, dynamic> json) {
    return ComplaintData(
      label: json['label'] ?? '',
      value: json['value'] ?? '',
    );
  }
}

class FileData {
  final String fileName;

  FileData({required this.fileName});

  factory FileData.fromJson(Map<String, dynamic> json) {
    return FileData(fileName: json['file_name'] ?? '');
  }
}