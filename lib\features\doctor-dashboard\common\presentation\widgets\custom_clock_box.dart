import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/widgets/labelled-widget.dart';

class Custom<PERSON>lockBox extends StatefulWidget {
  final String? hintText;
  final String? label;
  final DateTime? value;
  final bool isRequired;
  final ValueChanged<DateTime>? onChanged;
  const CustomClockBox({
    super.key,
    this.hintText,
    this.label,
    this.value,
    this.isRequired = false,
    this.onChanged,
  });

  @override
  State<CustomClockBox> createState() => _CustomClockBoxState();
}

class _CustomClockBoxState extends State<CustomClockBox> {
  late String formattedTime;

  @override
  void initState() {
    super.initState();
    if (widget.value != null) {
      final hour = widget.value!.hour % 12 == 0 ? 12 : widget.value!.hour % 12;
      final minute = widget.value!.minute.toString().padLeft(2, '0');
      final period = widget.value!.hour >= 12 ? 'PM' : 'AM';
      formattedTime = "$hour:$minute $period";
    } else {
      formattedTime = "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return LabelledWidget(
      label: widget.label,
      isRequired: widget.isRequired,
      border: BorderSide(
        style: BorderStyle.solid,
        color: AppConstants.borderColor,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: MouseRegion(
        cursor: WidgetStateMouseCursor.clickable,
        child: GestureDetector(
          onTap: () {
            showTimePicker(
              context: context,
              initialTime:
                  widget.value != null
                      ? TimeOfDay(
                        hour: widget.value!.hour,
                        minute: widget.value!.minute,
                      )
                      : TimeOfDay.now(),
            ).then((selectedTime) {
              if (selectedTime != null) {
                setState(() {
                  final now = DateTime.now();
                  final selectedDateTime = DateTime(
                    now.year,
                    now.month,
                    now.day,
                    selectedTime.hour,
                    selectedTime.minute,
                  );
                  final hour =
                      selectedTime.hour % 12 == 0 ? 12 : selectedTime.hour % 12;
                  final minute = selectedTime.minute.toString().padLeft(2, '0');
                  final period = selectedTime.hour >= 12 ? 'PM' : 'AM';
                  formattedTime = "$hour:$minute $period";

                  widget.onChanged?.call(selectedDateTime);
                });
              }
            });
          },
          child: Container(
            color: Colors.transparent,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              spacing: 10,
              children: [
                Text(
                  formattedTime.isEmpty
                      ? widget.hintText ?? "Select Time"
                      : formattedTime,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color:
                        formattedTime.isEmpty
                            ? AppConstants.inputFieldForegroundColor.withValues(
                              alpha: 0.6,
                            )
                            : AppConstants.inputFieldForegroundColor,
                  ),
                ),

                Icon(
                  Icons.access_time,
                  color: AppConstants.inputFieldForegroundColor.withValues(
                    alpha: 0.6,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
