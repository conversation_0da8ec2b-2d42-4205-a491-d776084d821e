import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_investigation_list_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_investigation_list/get_investigation_list_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_investigation_list/get_investigation_list_response_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_investigation_list_repositories.dart';

class GetInvestigationListRepositoriesImpl
    implements GetInvestigationListRepositories {
  final GetInvestigationListRemoteDatasource remoteDatasource;

  GetInvestigationListRepositoriesImpl({required this.remoteDatasource});

  @override
  Future<Either<Failure, GetInvestigationListResponseModel>>
  getAllInvestigations(GetInvestigationListRequestModel requestModel) async {
    try {
      final response = await remoteDatasource.getAllInvestigations(
        requestModel,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
