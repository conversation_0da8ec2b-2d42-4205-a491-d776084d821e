import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:imed_fe/core/router/app_router.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/presentation/pages/dashboard.dart';

final dashboardRoutes = [
  GoRoute(
    path: AppRouter.dashboard,
    builder: (BuildContext context, GoRouterState state) {
      return DashboardScreen();
    },
  ),
];
