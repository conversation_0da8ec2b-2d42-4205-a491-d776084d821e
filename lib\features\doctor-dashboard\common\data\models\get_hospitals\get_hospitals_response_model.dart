import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_hospitals/get_hospitals_data.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_hospitals/get_hospitals_response.dart';

class GetHospitalsResponseModel extends GetHospitalResponse {
  const GetHospitalsResponseModel({
    required super.data,
    required super.total,
    required super.skip,
    required super.limit,
    required super.message,
    required super.status,
    required super.statusCode,
  });

  factory GetHospitalsResponseModel.fromJson(Map<String, dynamic> json) {
    return GetHospitalsResponseModel(
      data: GetHospitalsData(
        data:
            (json['data'] as List)
                .map(
                  (item) => Hospital(
                    value: item['value'] as String,
                    label: item['label'] as String,
                  ),
                )
                .toList(),
      ),
      total: json['total'],
      skip: json['skip'],
      limit: json['limit'],
      message: json['message'],
      status: json['status'],
      statusCode: json['statusCode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data':
          data.data
              .map((item) => {'value': item.value, 'label': item.label})
              .toList(),
      'message': message,
      'status': status,
      'statusCode': statusCode,
    };
  }
}
