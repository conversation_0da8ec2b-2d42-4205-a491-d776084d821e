// This file will define different failure types.

import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;

  const Failure({this.message = 'An unexpected error occurred'});

  @override
  List<Object?> get props => [message];
}

// General failures
class ServerFailure extends Failure {
  final int? statusCode;

  const ServerFailure({super.message = 'Server Error', this.statusCode});

  @override
  List<Object?> get props => [message, statusCode];
}

class CacheFailure extends Failure {
  const CacheFailure({super.message = 'Cache Error'});
}

class NetworkFailure extends Failure {
  const NetworkFailure({
    super.message = 'Network Error. Please check your connection',
  });
}

class UnauthorizedFailure extends Failure {
  const UnauthorizedFailure({
    super.message = 'Unauthorized. Please login again',
  });
}
