import 'package:equatable/equatable.dart';

class GetDepartmentsData extends Equatable {
  final List<GetDepartmentsDataItems> departments;

  const GetDepartmentsData({required this.departments});

  @override
  List<Object?> get props => [departments];
}

class GetDepartmentsDataItems extends Equatable {
  final String value;
  final String label;

  const GetDepartmentsDataItems({required this.value, required this.label});
  @override
  List<Object?> get props => [value, label];
}
