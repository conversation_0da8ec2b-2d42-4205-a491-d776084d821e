import 'dart:convert';
import 'package:dio/dio.dart';

void main() async {
  // Test the exact same request that the Flutter app would make
  final dio = Dio();
  
  // Configure exactly like the Flutter app
  dio.options.baseUrl = 'http://10.10.1.5:3000';
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);
  dio.options.headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Im1VRzZ2WW5SbG9mdUx4Y2lacnU3U3B0ZjZOTSJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.geahFGDgu38Ac_6XnoC-j7QIoBErqbTNVQI-oeIXgf9ZMERtpDfNeWIhrGrZOHY4GjAejj8Kb6Lajge52B7MUbO9Gh6ADQmd1ZkAvFx9sPJAQjn0dQS_pCkCxVstoUvWRA-n6xY4o0148sfVD-GI0Mct_TZhMNF7lxk8gICnn83vY2fu-lPiU5lXFy59svq1cWicuNBjk7UlNfPTHmDahjdFcg8CYahUeorrDi9ZttadcmKu7XHM0npjgOtMB4lR8XwzlYbGvIt_V-7MA1lRpY2JR6GvShpoKwxBV-6qS9bagnX_OFgQHi98b62-UzHnnJbByFD3eDqaY2xPT1cBrg',
  };

  // Sample request body that matches the Flutter app structure
  final requestBody = {
    'start_date': '2024-12-05',
    'end_date': '2025-01-04',
    'is_repeat': false,
    'unavailable_dates': [],
    'days': [
      {
        'day': 'monday',
        'is_off_day': false,
        'slots': [
          {
            'start_time': '09:00',
            'end_time': '17:00',
          }
        ],
      },
      {
        'day': 'tuesday',
        'is_off_day': true,
        'slots': [],
      },
    ],
  };

  print('🔍 Testing POST request to availability endpoint...');
  print('🌐 URL: ${dio.options.baseUrl}/doctors/availability');
  print('📦 Request Body:');
  print(const JsonEncoder.withIndent('  ').convert(requestBody));

  try {
    final response = await dio.post(
      '/doctors/availability',
      data: requestBody,
    );
    
    print('✅ SUCCESS!');
    print('📊 Status Code: ${response.statusCode}');
    print('📦 Response Data:');
    print(const JsonEncoder.withIndent('  ').convert(response.data));
    
  } catch (e) {
    print('❌ ERROR!');
    if (e is DioException) {
      print('💥 Error Type: ${e.type}');
      print('📊 Status Code: ${e.response?.statusCode}');
      print('🔥 Error Message: ${e.message}');
      if (e.response?.data != null) {
        print('📦 Error Response:');
        print(const JsonEncoder.withIndent('  ').convert(e.response!.data));
      }
    } else {
      print('🔥 Unexpected Error: $e');
    }
  }
}
