import 'package:dartz/dartz.dart';
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/core/usecases/usecase.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_all_address_type/get_all_address_type_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_all_address_type/get_all_address_type_request.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_all_address_type/get_all_address_type_response.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_all_address_type_repositories.dart';

class GetAllAddressType
    implements UseCase<GetAllAddressTypeResponse, GetAllAddressTypeRequest> {
  final GetAllAddressTypeRepositories repository;

  GetAllAddressType(this.repository);

  @override
  Future<Either<Failure, GetAllAddressTypeResponse>> call(
    GetAllAddressTypeRequest params,
  ) async {
    final requestModel = GetAllAddressTypeRequestModel.fromEntity(params);
    return await repository.getAllAddressType(requestModel);
  }
}
