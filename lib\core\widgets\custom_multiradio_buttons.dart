import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';

class CustomMultiRadioButtons extends StatefulWidget {
  final int selectedIndex;
  final List<String> options;
  final ValueChanged<int> onChanged;
  const CustomMultiRadioButtons({
    super.key,
    required this.selectedIndex,
    required this.options,
    required this.onChanged,
  });

  @override
  State<CustomMultiRadioButtons> createState() =>
      _CustomMultiRadioButtonsState();
}

class _CustomMultiRadioButtonsState extends State<CustomMultiRadioButtons> {
  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 12,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < widget.options.length; i++)
          Row(
            children: [
              Radio<int>(
                value: i,
                groupValue: widget.selectedIndex,
                onChanged: (value) {
                  if (value != null) {
                    widget.onChanged(value);
                  }
                },
                activeColor: AppConstants.primaryColor,
                fillColor: WidgetStateProperty.all(AppConstants.primaryColor),
              ),
              Text(
                widget.options[i],
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: AppConstants.inputFieldForegroundColor,
                ),
              ),
            ],
          ),
      ],
    );
  }
}
