import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_investigation_list/get_investigation_list_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_investigation_list/get_investigation_list_response_model.dart';

abstract class GetInvestigationListRepositories {
  Future<Either<Failure, GetInvestigationListResponseModel>>
  getAllInvestigations(GetInvestigationListRequestModel requestModel);
}
