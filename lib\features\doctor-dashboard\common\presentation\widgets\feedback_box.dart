import 'package:flutter/material.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';
import 'package:imed_fe/core/widgets/reusable_svg_image.dart';
import 'package:imed_fe/features/doctor-dashboard/common/presentation/widgets/custom_input_box.dart';

class FeedbackBox extends StatelessWidget {
  const FeedbackBox({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 526,
      height: 636,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(11),
      ),
      child: Column(
        spacing: 20,
        children: [
          Row(
            children: [
              Text(
                'Contact iMed Helpdesk for product related difficulties',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFFED1C24),
                ),
              ),
              Spacer(),
              IconButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                icon: ReusableSvgImage(
                  assetPath: "assets/icons/doctor/close-icon.svg",
                  width: 24,
                  height: 24,
                ),
              ),
            ],
          ),
          Divider(color: AppConstants.borderColor, height: 1),
          Divider(color: AppConstants.borderColor, height: 1),
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: AppConstants.borderColor),
            ),
            child: CustomInputBox(
              hintText:
                  "Please describe your issues and what support you are looking for ...",
              controller: TextEditingController(),
              maxLines: 16,
            ),
          ),
          _ActionButtons(
            onSubmit: () {
              // Handle submit action
            },
          ),
        ],
      ),
    );
  }
}

class _ActionButtons extends StatelessWidget {
  final VoidCallback? onSubmit;
  const _ActionButtons({required this.onSubmit});

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 16,
      children: [
        SizedBox(
          width: 123,
          height: 40,
          child: CustomElevatedButton(
            borderRadius: 5,
            text: "Submit",

            onPressed: () {
              if (onSubmit != null) {
                onSubmit!();
              }
            },
          ),
        ),
        SizedBox(
          width: 123,
          height: 40,
          child: CustomOutlinedButton(
            borderRadius: 5,
            borderColor: AppConstants.textMidColor,
            foregroundColor: AppConstants.textMidColor,
            onPressed: () {
              Navigator.of(context).pop();
            },
            text: "Cancel",
          ),
        ),
      ],
    );
  }
}
