import 'package:imed_fe/core/error/exceptions.dart';
import 'package:imed_fe/core/network/dio_client.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_investigation_list/get_investigation_list_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_investigation_list/get_investigation_list_response_model.dart';

abstract class GetInvestigationListRemoteDatasource {
  Future<GetInvestigationListResponseModel> getAllInvestigations(
    GetInvestigationListRequestModel requestModel,
  );
}

class GetInvestigationListRemoteDatasourceImpl
    implements GetInvestigationListRemoteDatasource {
  final DioClient dioClient;

  GetInvestigationListRemoteDatasourceImpl({required this.dioClient});

  @override
  Future<GetInvestigationListResponseModel> getAllInvestigations(
    GetInvestigationListRequestModel request,
  ) async {
    try {
      // final queryParams = request.toJson();

      // final response = await dioClient.get(
      //   '{{devURL}}/doctors/investigations?skip=0&limit=10',
      // );
      // return GetInvestigationListResponseModel.fromJson(response);

      await Future.delayed(const Duration(seconds: 2));

      final responseJson = {
        "data": [
          {"name": "RBS", "description": "Random Blood Sugar", "value": "rbs"},
          {
            "name": "CBC",
            "description": "Complete Blood Count",
            "value": "cbc",
          },
          {"name": "WBC", "description": "White Blood Count", "value": "wbc"},
        ],
        "total": 3,
        "skip": "0",
        "limit": "10",
        "message": "Doctor investigations retrieved successfully.",
        "status": "success",
        "statusCode": 200,
      };

      return GetInvestigationListResponseModel.fromJson(responseJson);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
