import 'package:equatable/equatable.dart';
import 'time_slot.dart';

class DoctorAvailability extends Equatable {
  final String doctorId;
  final String doctorName;
  final DateTime date;
  final List<TimeSlot> timeSlots;
  final bool isAvailable;

  const DoctorAvailability({
    required this.doctorId,
    required this.doctorName,
    required this.date,
    required this.timeSlots,
    required this.isAvailable,
  });

  @override
  List<Object?> get props => [
        doctorId,
        doctorName,
        date,
        timeSlots,
        isAvailable,
      ];

  DoctorAvailability copyWith({
    String? doctorId,
    String? doctorName,
    DateTime? date,
    List<TimeSlot>? timeSlots,
    bool? isAvailable,
  }) {
    return DoctorAvailability(
      doctorId: doctorId ?? this.doctorId,
      doctorName: doctorName ?? this.doctorName,
      date: date ?? this.date,
      timeSlots: timeSlots ?? this.timeSlots,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  List<TimeSlot> get availableTimeSlots =>
      timeSlots.where((slot) => slot.isAvailable).toList();
}
