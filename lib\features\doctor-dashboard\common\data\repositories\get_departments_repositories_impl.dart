import "package:dartz/dartz.dart";
import 'package:imed_fe/core/error/failures.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/datasource/remote/get_departments_remote_datasource.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_departments/get_departments_request_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/data/models/get_departments/get_departments_response_model.dart';
import 'package:imed_fe/features/doctor-dashboard/common/domain/repositories/get_departments_repositories.dart';

class GetDepartmentsRepositoriesImpl implements GetDepartmentsRepositories {
  final GetDepartmentsRemoteDatasource remoteDatasource;

  GetDepartmentsRepositoriesImpl({required this.remoteDatasource});

  @override
  Future<Either<Failure, GetDepartmentsResponseModel>> getAllDepartments(
    GetDepartmentsRequestModel requestModel,
  ) async {
    try {
      final response = await remoteDatasource.getAllDepartments(requestModel);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
