import 'package:imed_fe/features/doctor-dashboard/common/domain/entities/get_all_address_type/get_all_address_type_request.dart';

class GetAllAddressTypeRequestModel extends GetAllAddressTypeRequest {
  const GetAllAddressTypeRequestModel();

  factory GetAllAddressTypeRequestModel.fromEntity(
    GetAllAddressTypeRequest entity,
  ) {
    return GetAllAddressTypeRequestModel();
  }

  Map<String, dynamic> toJson() {
    return {};
  }
}
