import 'package:equatable/equatable.dart';
import 'package:imed_fe/features/doctor-dashboard/main-dashboard/domain/entities/analytics/get_doctor_appointment_statistics/get_doctor_appointment_statistics_data.dart';

class GetDoctorAppointmentStatisticsResponse extends Equatable {
  final GetDoctorAppointmentStatisticsData data;
  final String message;
  final String status;
  final int statusCode;
  const GetDoctorAppointmentStatisticsResponse({
    required this.data,
    required this.message,
    required this.status,
    required this.statusCode,
  });

  @override
  List<Object?> get props => [data, message, status, statusCode];
}
