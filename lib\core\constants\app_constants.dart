import 'package:flutter/material.dart';

class AppConstants {
  // API
  static const String baseUrl = 'https://api.imed.com';

  // App Theme
  static const Color primaryColor = Color(0xFF2260FF);
  static const Color secondaryColor = Color(0xFF4FC3F7);
  static const Color backgroundColor = Color(0xFFFFFFFF);
  static const Color errorColor = Color(0xFFDC2626);
  // Shadow background color
  static const Color shadowBackgroundColor = Color(0xFFE0E3EA);

  // Text Colors
  static const Color textPrimaryColor = Color(0xFF000000);
  static const Color textHighColor = Color(0xFF3E3E4C);
  static const Color textMidColor = Color(0xFF6C6E74);

  // Input Field Colors
  static const Color inputFieldForegroundColor = Color(0xFF718DE0);
  static const Color inputFieldBackgroundColor = Color(0xFFF2F5FF);

  static const Color inputFieldBackgroundColor2 = Color(0xFFEDF2FF);

  // Border Colors
  static const Color borderColor = Color(0xFFE0E3EA);
  static const Color borderColor2 = Color(0xFFE6E7EB);

  // Elevated Button
  static const Color elevatedButtonBackgroundColor = Color(0xFF2563EB);
  static const Color elevatedButtonForegroundColor = Color(0xFFFFFFFF);
  static const double buttonWidth = double.infinity;
  static const double buttonHeight = 48.0;

  // Text Sizes
  static const double fontSize14 = 14.0;
  static const double fontSize20 = 20.0;
  static const double fontSize22 = 22.0;
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 18.0;
  static const double fontSizeExtraLarge = 24.0;

  // Padding & Margins
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingExtraLarge = 32.0;

  // SizedBox Heights
  static const double sizedBoxHeightSmall = 8.0;
  static const double sizedBoxHeightMedium = 16.0;
  static const double sizedBoxHeightLarge = 24.0;
  static const double sizedBoxHeightExtraLarge = 32.0;

  // Border Radius
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 16.0;
  static const double borderRadiusLarge = 24.0;
  static const double borderRadiusExtraLarge = 32.0;
}
