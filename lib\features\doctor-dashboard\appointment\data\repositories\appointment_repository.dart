import '../../domain/entities/appointment_entity.dart';
import '../../domain/entities/new_appointment_request.dart';
import '../models/appointment_detail_model.dart';
import '../models/new_appointment_model.dart';

abstract class AppointmentRepository {
  Future<List<AppointmentEntity>> getAppointments();
  Future<AppointmentDetail> getAppointmentDetail(String appointmentId);
  Future<NewAppointmentModel> createAppointment(NewAppointmentRequest request);
  Future<void> cancelAppointment(String appointmentId);
  Future<void> confirmAppointment(String appointmentId);
}