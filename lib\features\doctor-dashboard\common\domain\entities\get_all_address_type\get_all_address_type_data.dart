import 'package:equatable/equatable.dart';

class GetAllAddressTypeData extends Equatable {
  final List<GetAllAddressTypeDataItems> data;

  const GetAllAddressTypeData({required this.data});

  @override
  List<Object?> get props => [data];
}

class GetAllAddressTypeDataItems extends Equatable {
  final String id;
  final String code;
  final String label;
  final String? description;

  const GetAllAddressTypeDataItems({
    required this.id,
    required this.code,
    required this.label,
    this.description,
  });

  @override
  List<Object?> get props => [id, code, label, description];
}
